#!/usr/bin/env python3
"""
Reddit2ShortsCLI - Generate TikTok-ready short videos from Reddit posts.

Usage:
    python reddit2shorts.py --subreddit AITA --limit 100 --output 5
"""

import os
import sys
import time
import logging
import click
from pathlib import Path
from dotenv import load_dotenv

# Import our modules
from reddit_fetcher import RedditFetcher
from post_grader import PostGrader
from tts_generator import TTSGenerator
from video_composer import VideoComposer

# Load environment variables
load_dotenv()

class Reddit2ShortsCLI:
    def __init__(self):
        """Initialize the Reddit2Shorts CLI application."""
        self.reddit_fetcher = None
        self.post_grader = None
        self.tts_generator = None
        self.video_composer = None
        
        # Setup logging
        self._setup_logging()
        
        # Initialize components
        self._initialize_components()
    
    def _setup_logging(self):
        """Set up logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('reddit2shorts.log')
            ]
        )
    
    def _initialize_components(self):
        """Initialize all components with error handling."""
        try:
            logging.info("Initializing Reddit2Shorts components...")
            
            self.reddit_fetcher = RedditFetcher()
            self.post_grader = PostGrader()
            self.tts_generator = TTSGenerator()
            self.video_composer = VideoComposer()
            
            logging.info("All components initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize components: {e}")
            raise
    
    def _validate_environment(self):
        """Validate that all required environment variables and files are present."""
        required_env_vars = ['REDDIT_CLIENT_ID', 'REDDIT_CLIENT_SECRET', 'OPENAI_API_KEY']
        missing_vars = []
        
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            logging.error("Please create a .env file with the required API credentials")
            return False
        
        # Check for B-roll videos
        broll_dir = Path('broll')
        if not broll_dir.exists() or not list(broll_dir.glob('*.mp4')):
            logging.warning("No B-roll videos found in 'broll' directory")
            logging.warning("Please add some gameplay videos for background footage")
            return False
        
        return True
    
    def generate_videos(self, subreddit: str, limit: int, output: int, voice: str = 'onyx'):
        """
        Main pipeline to generate videos from Reddit posts.
        
        Args:
            subreddit: Name of the subreddit to scrape
            limit: Number of posts to fetch (max 100)
            output: Number of videos to generate (max 10)
            voice: Voice to use for TTS
        """
        start_time = time.time()
        
        try:
            logging.info(f"Starting Reddit2Shorts pipeline:")
            logging.info(f"  Subreddit: r/{subreddit}")
            logging.info(f"  Posts to fetch: {limit}")
            logging.info(f"  Videos to generate: {output}")
            logging.info(f"  TTS Voice: {voice}")
            
            # Step 1: Fetch and filter posts
            logging.info("\n=== Step 1: Fetching Reddit posts ===")
            posts = self.reddit_fetcher.get_posts_for_processing(subreddit, limit)
            
            if not posts:
                logging.error("No valid posts found after filtering")
                return False
            
            logging.info(f"Found {len(posts)} valid posts after filtering")
            
            # Step 2: Grade and rank posts
            logging.info("\n=== Step 2: Grading posts with AI ===")
            graded_posts = self.post_grader.grade_posts_batch(posts)
            
            if not graded_posts:
                logging.error("No posts could be graded")
                return False
            
            # Step 3: Select top posts
            top_posts = self.post_grader.select_top_posts(graded_posts, output)
            
            if not top_posts:
                logging.error("No top posts selected")
                return False
            
            # Step 4: Generate audio narration
            logging.info("\n=== Step 3: Generating audio narration ===")
            audio_files = self.tts_generator.generate_audio_batch(top_posts, voice)
            
            if not audio_files:
                logging.error("No audio files generated")
                return False
            
            # Step 5: Create videos
            logging.info("\n=== Step 4: Creating videos ===")
            video_files = self.video_composer.create_videos_batch(top_posts, audio_files)
            
            if not video_files:
                logging.error("No videos created")
                return False
            
            # Success summary
            elapsed_time = time.time() - start_time
            logging.info(f"\n=== Pipeline completed successfully! ===")
            logging.info(f"Generated {len(video_files)} videos in {elapsed_time:.2f} seconds")
            logging.info(f"Videos saved to: {self.video_composer.output_dir}")
            
            # List generated videos
            for post_id, video_path in video_files.items():
                post = next(p for p in top_posts if p['id'] == post_id)
                logging.info(f"  📹 {Path(video_path).name} - {post['title'][:50]}...")
            
            # Cleanup temporary audio files
            self.tts_generator.cleanup_temp_audio()
            
            return True
            
        except Exception as e:
            logging.error(f"Pipeline failed: {e}")
            return False

@click.command()
@click.option('--subreddit', required=True, help='Name of the subreddit to scrape from')
@click.option('--limit', default=50, type=click.IntRange(1, 100), help='Number of top posts to pull (max: 100)')
@click.option('--output', default=3, type=click.IntRange(1, 10), help='Number of final videos to generate (max: 10)')
@click.option('--voice', default='onyx', type=click.Choice(['onyx', 'echo', 'fable', 'nova', 'shimmer', 'alloy']), help='Voice to use for TTS')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def main(subreddit, limit, output, voice, verbose):
    """
    Reddit2ShortsCLI - Generate TikTok-ready short videos from Reddit posts.
    
    This tool fetches posts from a specified subreddit, grades them using AI,
    generates audio narration, and creates short videos with gameplay footage.
    """
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize the CLI application
        app = Reddit2ShortsCLI()
        
        # Validate environment
        if not app._validate_environment():
            click.echo("❌ Environment validation failed. Please check the logs for details.")
            sys.exit(1)
        
        click.echo(f"🚀 Starting Reddit2Shorts for r/{subreddit}")
        click.echo(f"📊 Fetching {limit} posts, generating {output} videos")
        
        # Run the pipeline
        success = app.generate_videos(subreddit, limit, output, voice)
        
        if success:
            click.echo("✅ Videos generated successfully!")
            click.echo(f"📁 Check the 'output_videos' directory for your videos")
        else:
            click.echo("❌ Video generation failed. Check the logs for details.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        click.echo("\n⏹️  Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Unexpected error: {e}")
        logging.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
