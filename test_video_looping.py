#!/usr/bin/env python3
"""
Test script to verify video looping functionality works correctly.
"""

import os
import subprocess
import logging
from pathlib import Path

def create_test_audio(duration_seconds: float, filename: str) -> str:
    """Create a test audio file of specified duration."""
    output_path = Path('temp_audio') / filename
    output_path.parent.mkdir(exist_ok=True)
    
    # Create a test tone audio file
    cmd = [
        'ffmpeg',
        '-f', 'lavfi',
        '-i', f'sine=frequency=440:duration={duration_seconds}',
        '-c:a', 'mp3',
        '-y',
        str(output_path)
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"✅ Created test audio: {filename} ({duration_seconds}s)")
        return str(output_path)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create test audio: {e}")
        return None

def get_duration(file_path: str) -> float:
    """Get duration of media file using ffprobe."""
    try:
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            file_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return float(result.stdout.strip())
    except Exception as e:
        print(f"Error getting duration for {file_path}: {e}")
        return 0.0

def test_video_looping():
    """Test video looping with different audio durations."""
    print("🧪 Testing Video Looping Functionality")
    print("=" * 50)
    
    # Check if we have B-roll videos
    broll_dir = Path('broll')
    broll_videos = list(broll_dir.glob('*.mp4'))
    
    if not broll_videos:
        print("❌ No B-roll videos found. Run setup_broll.py first.")
        return False
    
    print(f"📁 Found {len(broll_videos)} B-roll videos:")
    for video in broll_videos:
        duration = get_duration(str(video))
        print(f"   - {video.name}: {duration:.2f}s")
    
    # Test scenarios
    test_cases = [
        {"name": "Short Audio (30s)", "duration": 30, "filename": "test_short_30s.mp3"},
        {"name": "Medium Audio (90s)", "duration": 90, "filename": "test_medium_90s.mp3"},
        {"name": "Long Audio (150s)", "duration": 150, "filename": "test_long_150s.mp3"},
    ]
    
    print(f"\n🎵 Creating test audio files...")
    for test_case in test_cases:
        audio_path = create_test_audio(test_case["duration"], test_case["filename"])
        if audio_path:
            test_case["audio_path"] = audio_path
    
    # Test video creation
    print(f"\n🎬 Testing video creation...")
    
    try:
        from video_composer import VideoComposer
        composer = VideoComposer()
        
        for i, test_case in enumerate(test_cases):
            if "audio_path" not in test_case:
                continue
                
            print(f"\n--- Test {i+1}: {test_case['name']} ---")
            
            test_post = {
                'id': f'test_{i+1}',
                'title': f'Test post {i+1}',
                'summary': f'test_video_{i+1}'
            }
            
            video_path = composer.create_video(test_post, test_case["audio_path"])
            
            if video_path:
                video_duration = get_duration(video_path)
                audio_duration = test_case["duration"]
                duration_diff = abs(video_duration - audio_duration)
                
                print(f"✅ Video created: {Path(video_path).name}")
                print(f"   Audio duration: {audio_duration:.2f}s")
                print(f"   Video duration: {video_duration:.2f}s")
                print(f"   Difference: {duration_diff:.2f}s")
                
                if duration_diff <= 1.0:  # Allow 1 second tolerance
                    print(f"   ✅ PASS: Duration match within tolerance")
                else:
                    print(f"   ❌ FAIL: Duration mismatch > 1s")
            else:
                print(f"❌ Failed to create video for {test_case['name']}")
    
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    
    print(f"\n🧹 Cleaning up test files...")
    # Clean up test audio files
    for test_case in test_cases:
        if "audio_path" in test_case:
            try:
                Path(test_case["audio_path"]).unlink()
                print(f"   Deleted: {test_case['filename']}")
            except Exception as e:
                print(f"   Warning: Could not delete {test_case['filename']}: {e}")
    
    print(f"\n✅ Video looping test completed!")
    return True

if __name__ == "__main__":
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    test_video_looping()
