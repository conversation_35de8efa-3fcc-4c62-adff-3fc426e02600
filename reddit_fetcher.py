"""
Reddit API integration using PRAW to fetch and process posts.
"""

import os
import praw
import logging
from typing import List, Dict, Optional
from dotenv import load_dotenv
from content_filter import ContentFilter

# Load environment variables
load_dotenv()

class RedditFetcher:
    def __init__(self):
        """Initialize Reddit API client with authentication."""
        self.reddit = None
        self.content_filter = ContentFilter()
        self._setup_reddit_client()
    
    def _setup_reddit_client(self):
        """Set up Reddit API client with credentials."""
        try:
            client_id = os.getenv('REDDIT_CLIENT_ID')
            client_secret = os.getenv('REDDIT_CLIENT_SECRET')
            user_agent = os.getenv('REDDIT_USER_AGENT', 'Reddit2ShortsCLI/1.0')
            
            if not client_id or not client_secret:
                raise ValueError("Reddit API credentials not found in environment variables")
            
            self.reddit = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent
            )
            
            # Test the connection
            self.reddit.user.me()
            logging.info("Reddit API client initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize Reddit API client: {e}")
            raise
    
    def fetch_posts(self, subreddit_name: str, limit: int = 100) -> List[Dict]:
        """
        Fetch top posts from a subreddit.
        
        Args:
            subreddit_name: Name of the subreddit
            limit: Maximum number of posts to fetch (max 100)
            
        Returns:
            List of post dictionaries with metadata
        """
        if not self.reddit:
            raise RuntimeError("Reddit client not initialized")
        
        # Ensure limit doesn't exceed 100
        limit = min(limit, 100)
        
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            posts = []
            
            logging.info(f"Fetching top {limit} posts from r/{subreddit_name}")
            
            # Fetch hot posts (you can change to 'top' for different time periods)
            for submission in subreddit.hot(limit=limit):
                # Skip stickied posts
                if submission.stickied:
                    continue
                
                # Skip posts without content
                if not submission.selftext and not submission.title:
                    continue
                
                post_data = {
                    'id': submission.id,
                    'title': submission.title,
                    'content': submission.selftext,
                    'score': submission.score,
                    'upvote_ratio': submission.upvote_ratio,
                    'num_comments': submission.num_comments,
                    'created_utc': submission.created_utc,
                    'url': submission.url,
                    'permalink': f"https://reddit.com{submission.permalink}",
                    'author': str(submission.author) if submission.author else '[deleted]',
                    'subreddit': subreddit_name,
                    'is_nsfw': submission.over_18,
                    'is_self': submission.is_self
                }
                
                posts.append(post_data)
            
            logging.info(f"Successfully fetched {len(posts)} posts")
            return posts
            
        except Exception as e:
            logging.error(f"Error fetching posts from r/{subreddit_name}: {e}")
            raise
    
    def filter_and_clean_posts(self, posts: List[Dict]) -> List[Dict]:
        """
        Filter and clean posts using the content filter.
        
        Args:
            posts: List of raw post dictionaries
            
        Returns:
            List of filtered and cleaned posts with filter metadata
        """
        filtered_posts = []
        
        for post in posts:
            # Skip NSFW posts immediately
            if post.get('is_nsfw', False):
                logging.debug(f"Skipping NSFW post: {post['id']}")
                continue
            
            # Skip non-self posts (links, images, etc.)
            if not post.get('is_self', True):
                logging.debug(f"Skipping non-self post: {post['id']}")
                continue
            
            # Apply content filter
            filter_result = self.content_filter.filter_post(
                post['title'], 
                post['content']
            )
            # logging.info(f"Filter result for post {post['id']}: {filter_result}")
            
            # Skip invalid posts
            if not filter_result['is_valid']:
                logging.info(f"Skipping invalid post {post['id']}: {filter_result['reasons']}")
                continue
            
            # Add filter metadata to post
            post.update({
                'word_count': filter_result['word_count'],
                'length_penalty': filter_result['length_penalty'],
                'cleaned_content': filter_result['cleaned_content'],
                'filter_reasons': filter_result['reasons']
            })
            
            filtered_posts.append(post)
        
        logging.info(f"Filtered {len(posts)} posts down to {len(filtered_posts)} valid posts")
        return filtered_posts
    
    def get_posts_for_processing(self, subreddit_name: str, limit: int = 100) -> List[Dict]:
        """
        Complete pipeline to fetch, filter, and clean posts.
        
        Args:
            subreddit_name: Name of the subreddit
            limit: Maximum number of posts to fetch
            
        Returns:
            List of processed posts ready for grading
        """
        # Fetch raw posts
        raw_posts = self.fetch_posts(subreddit_name, limit)
        
        # Filter and clean posts
        processed_posts = self.filter_and_clean_posts(raw_posts)
        
        return processed_posts

def test_reddit_fetcher():
    """Test the Reddit fetcher with a sample subreddit."""
    try:
        fetcher = RedditFetcher()
        
        # Test with a small number of posts
        posts = fetcher.get_posts_for_processing('AmItheAsshole', limit=5)
        
        print(f"Fetched {len(posts)} posts:")
        for i, post in enumerate(posts[:3]):  # Show first 3
            print(f"\nPost {i+1}:")
            print(f"Title: {post['title']}")
            print(f"Score: {post['score']}")
            print(f"Word count: {post['word_count']}")
            print(f"Length penalty: {post['length_penalty']:.2f}")
            print(f"Content preview: {post['cleaned_content'][:100]}...")
            
    except Exception as e:
        print(f"Error testing Reddit fetcher: {e}")
        print("Make sure you have set up your .env file with Reddit API credentials")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    test_reddit_fetcher()
