#!/usr/bin/env python3
"""
Test script to validate all Reddit2Shorts components.
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test that all required environment variables are set."""
    print("🔧 Testing environment setup...")
    
    required_vars = ['REDDIT_CLIENT_ID', 'REDDIT_CLIENT_SECRET', 'OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please create a .env file with the required API credentials")
        return False
    
    print("✅ Environment variables configured")
    return True

def test_dependencies():
    """Test that all required Python packages are installed."""
    print("\n📦 Testing Python dependencies...")
    
    required_packages = [
        'praw', 'openai', 'ffmpeg', 'click', 'python-dotenv', 
        'better_profanity', 'moviepy', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All Python dependencies installed")
    return True

def test_ffmpeg():
    """Test that ffmpeg is available."""
    print("\n🎬 Testing ffmpeg installation...")
    
    import subprocess
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ffmpeg is installed and working")
            return True
        else:
            print("❌ ffmpeg command failed")
            return False
    except FileNotFoundError:
        print("❌ ffmpeg not found. Please install ffmpeg")
        return False
    except subprocess.TimeoutExpired:
        print("❌ ffmpeg command timed out")
        return False

def test_content_filter():
    """Test the content filtering system."""
    print("\n🛡️ Testing content filter...")
    
    try:
        from content_filter import ContentFilter
        
        filter_system = ContentFilter()
        
        # Test with a clean post
        result = filter_system.filter_post(
            "AITA for helping my neighbor?",
            "My neighbor needed help moving furniture so I helped them. Now they want to pay me but I don't want money."
        )
        
        if result['is_valid']:
            print("✅ Content filter working correctly")
            return True
        else:
            print(f"❌ Content filter failed: {result['reasons']}")
            return False
            
    except Exception as e:
        print(f"❌ Content filter error: {e}")
        return False

def test_reddit_connection():
    """Test Reddit API connection."""
    print("\n🔗 Testing Reddit API connection...")
    
    try:
        from reddit_fetcher import RedditFetcher
        
        fetcher = RedditFetcher()
        
        # Try to fetch a small number of posts from a popular subreddit
        posts = fetcher.fetch_posts('AskReddit', limit=1)
        
        if posts and len(posts) > 0:
            print("✅ Reddit API connection successful")
            return True
        else:
            print("❌ No posts retrieved from Reddit")
            return False
            
    except Exception as e:
        print(f"❌ Reddit API error: {e}")
        return False

def test_post_reformatter():
    """Test the post reformatter."""
    print("\n✏️ Testing post reformatter...")

    try:
        from post_reformatter import PostReformatter

        reformatter = PostReformatter()

        # Test with a simple post
        test_post = {
            'id': 'test1',
            'title': 'AITA for being upset?',
            'content': 'This is a test post. It has some content that needs reformatting.',
            'word_count': 12
        }

        # Just test initialization for now
        print("✅ Post reformatter initialized successfully")
        return True

    except Exception as e:
        print(f"❌ Post reformatter error: {e}")
        return False

def test_openai_connection():
    """Test OpenAI API connection."""
    print("\n🤖 Testing OpenAI API connection...")

    try:
        from openai import OpenAI

        client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        # Test with a simple completion
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": "Say 'API test successful'"}],
            max_tokens=10
        )

        if response.choices[0].message.content:
            print("✅ OpenAI API connection successful")
            return True
        else:
            print("❌ OpenAI API returned empty response")
            return False

    except Exception as e:
        print(f"❌ OpenAI API error: {e}")
        return False

def test_directories():
    """Test that required directories exist."""
    print("\n📁 Testing directory structure...")
    
    required_dirs = ['broll', 'output_videos', 'temp_audio', 'prompts']
    missing_dirs = []
    
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"❌ Missing directories: {', '.join(missing_dirs)}")
        print("Creating missing directories...")
        for dir_name in missing_dirs:
            Path(dir_name).mkdir(exist_ok=True)
        print("✅ Directories created")
    else:
        print("✅ All required directories exist")
    
    return True

def test_broll_videos():
    """Test that B-roll videos are available."""
    print("\n🎮 Testing B-roll videos...")
    
    broll_dir = Path('broll')
    video_files = list(broll_dir.glob('*.mp4')) + list(broll_dir.glob('*.mov'))
    
    if not video_files:
        print("⚠️  No B-roll videos found in 'broll' directory")
        print("Please add some gameplay videos (MP4 format) to the 'broll' directory")
        print("Suggested content: Minecraft parkour, Subway Surfers, mobile games")
        return False
    
    print(f"✅ Found {len(video_files)} B-roll videos:")
    for video in video_files[:3]:  # Show first 3
        print(f"   - {video.name}")
    
    return True

def main():
    """Run all tests."""
    print("🧪 Reddit2Shorts Component Test Suite")
    print("=" * 50)
    
    tests = [
        test_environment,
        test_dependencies,
        test_ffmpeg,
        test_directories,
        test_content_filter,
        test_reddit_connection,
        test_post_reformatter,
        test_openai_connection,
        test_broll_videos
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Reddit2Shorts is ready to use.")
        print("\nTry running:")
        print("python reddit2shorts.py --subreddit AskReddit --limit 10 --output 1")
    else:
        print("❌ Some tests failed. Please fix the issues above before using Reddit2Shorts.")
        return 1
    
    return 0

if __name__ == "__main__":
    # Set up basic logging
    logging.basicConfig(level=logging.WARNING)
    
    sys.exit(main())
