# Reddit2Shorts Quick Start Guide

Get up and running with Reddit2Shorts in 5 minutes!

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Install ffmpeg
- **macOS**: `brew install ffmpeg`
- **Ubuntu**: `sudo apt install ffmpeg`
- **Windows**: Download from https://ffmpeg.org/

### 3. Set up API credentials
Copy the template and fill in your credentials:
```bash
cp .env.template .env
```

Edit `.env` with your API keys:
```
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
OPENAI_API_KEY=your_openai_api_key
```

### 4. Add B-roll videos
Option A - Use sample videos (for testing):
```bash
python setup_broll.py
```

Option B - Add your own gameplay videos to the `broll/` folder

### 5. Test the setup
```bash
python test_components.py
```

### 6. Generate your first video!
```bash
python reddit2shorts.py --subreddit AskReddit --limit 20 --output 1
```

## 🔑 Getting API Keys

### Reddit API (Free)
1. Go to https://www.reddit.com/prefs/apps
2. Click "Create App"
3. Choose "script" type
4. Copy the client ID and secret

### OpenAI API (Paid)
1. Go to https://platform.openai.com/api-keys
2. Create a new API key
3. Add credits to your account (~$5 should generate 50+ videos)

## 🎯 Best Subreddits to Try

- **AmItheAsshole** - Great drama and moral dilemmas
- **tifu** - Funny failure stories
- **relationship_advice** - Engaging relationship drama
- **AskReddit** - Wide variety of content
- **confession** - Personal stories
- **pettyrevenge** - Satisfying revenge stories

## 📱 Example Commands

Generate 3 videos from AITA:
```bash
python reddit2shorts.py --subreddit AmItheAsshole --limit 50 --output 3
```

Use a female voice:
```bash
python reddit2shorts.py --subreddit tifu --limit 30 --output 2 --voice nova
```

Enable detailed logging:
```bash
python reddit2shorts.py --subreddit relationship_advice --limit 40 --output 1 --verbose
```

## 🎬 Output

Videos are saved to `output_videos/` in TikTok-ready format:
- **Resolution**: 1080x1920 (9:16 aspect ratio)
- **Frame rate**: 30 FPS
- **Duration**: 1-1.5 minutes (based on content)
- **Format**: MP4 with H.264 video and AAC audio

## 🔧 Troubleshooting

**"No posts found"** - Try a different subreddit or increase the limit

**"No B-roll videos"** - Run `python setup_broll.py` or add videos to `broll/`

**"API key invalid"** - Double-check your `.env` file

**"ffmpeg not found"** - Install ffmpeg using your system's package manager

## 💡 Tips

- Start with small batches (1-2 videos) to test
- Popular subreddits work best
- Longer posts (100-300 words) get better scores
- Check your OpenAI usage to avoid unexpected costs
- Use `--verbose` flag to see detailed progress

## 🎉 You're Ready!

Your first video should be ready in 2-3 minutes. Check the `output_videos/` folder and enjoy your AI-generated content!
