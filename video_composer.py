"""
Video composition pipeline using ffmpeg to combine gameplay footage with narration.
"""

import os
import random
import logging
import subprocess
from pathlib import Path
from typing import Optional, List

# Try to import ffmpeg-python for duration checking, but it's optional
try:
    import ffmpeg
    FFMPEG_PYTHON_AVAILABLE = True
except ImportError:
    FFMPEG_PYTHON_AVAILABLE = False
    logging.warning("ffmpeg-python not available, using subprocess for duration checking")

class VideoComposer:
    def __init__(self):
        """Initialize video composer."""
        self.broll_dir = Path('broll')
        self.output_dir = Path('output_videos')
        self.temp_dir = Path('temp_audio')
        
        # Create directories if they don't exist
        self.output_dir.mkdir(exist_ok=True)
        
        # Video settings for TikTok format
        self.video_settings = {
            'width': 1080,
            'height': 1920,  # 9:16 aspect ratio for TikTok
            'fps': 30,
            'video_codec': 'libx264',
            'audio_codec': 'aac',
            'audio_bitrate': '128k',
            'video_bitrate': '2M'
        }
    
    def get_available_broll_videos(self) -> List[Path]:
        """Get list of available B-roll videos."""
        if not self.broll_dir.exists():
            logging.warning(f"B-roll directory {self.broll_dir} does not exist")
            return []
        
        # Look for common video formats
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
        broll_videos = []
        
        for ext in video_extensions:
            broll_videos.extend(self.broll_dir.glob(f'*{ext}'))
        
        logging.info(f"Found {len(broll_videos)} B-roll videos")
        return broll_videos
    
    def get_video_duration(self, video_path: str) -> Optional[float]:
        """Get duration of video file in seconds."""
        try:
            # Use moviepy to get video duration
            from moviepy.editor import VideoFileClip
            
            clip = VideoFileClip(video_path)
            duration = clip.duration
            clip.close()  # Important to close the clip to free resources
            return duration
        except ImportError:
            logging.warning("moviepy not installed, falling back to ffprobe")
            try:
                # Fallback to ffprobe
                result = subprocess.run(
                    ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', 
                     '-of', 'default=noprint_wrappers=1:nokey=1', video_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                if result.returncode == 0:
                    return float(result.stdout.strip())
            except Exception as e:
                logging.error(f"Fallback method failed: {e}")
            
            logging.warning(f"Could not determine duration for {video_path}")
            return None
        except Exception as e:
            logging.error(f"Error getting video duration for {video_path}: {e}")
            return None
    
    def get_audio_duration(self, audio_path: str) -> Optional[float]:
        """Get duration of audio file in seconds."""
        try:
            import mutagen
            from mutagen.mp3 import MP3

            audio = MP3(audio_path)
            return audio.info.length
        except ImportError:
            logging.warning("mutagen not installed, falling back to ffprobe")
            try:
                # Fallback to ffprobe
                result = subprocess.run(
                    ['ffprobe', '-v', 'error', '-show_entries', 'format=duration',
                     '-of', 'default=noprint_wrappers=1:nokey=1', audio_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                if result.returncode == 0:
                    return float(result.stdout.strip())
            except Exception as e:
                logging.error(f"Fallback method failed: {e}")

            logging.warning(f"Could not determine duration for {audio_path}")
            return None
        except Exception as e:
            logging.error(f"Error getting audio duration for {audio_path}: {e}")
            return None
    
    def select_random_broll(self, target_duration: float) -> Optional[Path]:
        """
        Select a random B-roll video. If target_duration is 0, any video is suitable.

        Args:
            target_duration: Required duration in seconds (0 means any duration is fine)

        Returns:
            Path to selected B-roll video or None if none available
        """
        available_videos = self.get_available_broll_videos()

        if not available_videos:
            logging.error("No B-roll videos available")
            return None

        # If target_duration is 0, any video is suitable (we'll loop if needed)
        if target_duration <= 0:
            selected_video = random.choice(available_videos)
            logging.info(f"Selected B-roll video: {selected_video.name}")
            return selected_video

        # Filter videos that are long enough for the target duration
        suitable_videos = []
        for video_path in available_videos:
            duration = self.get_video_duration(str(video_path))
            if duration and duration >= target_duration:
                suitable_videos.append(video_path)

        if not suitable_videos:
            logging.warning(f"No B-roll videos long enough for {target_duration:.2f}s, using longest available")
            # Use the longest available video
            longest_video = None
            longest_duration = 0
            for video_path in available_videos:
                duration = self.get_video_duration(str(video_path))
                if duration and duration > longest_duration:
                    longest_duration = duration
                    longest_video = video_path
            return longest_video

        # Select random suitable video
        selected_video = random.choice(suitable_videos)
        logging.info(f"Selected B-roll video: {selected_video.name}")
        return selected_video
    
    def create_video(self, post: dict, audio_path: str) -> Optional[str]:
        """
        Create final video by combining B-roll footage with narration.
        
        Args:
            post: Post dictionary with metadata
            audio_path: Path to generated audio file
            
        Returns:
            Path to generated video file or None if failed
        """
        try:
            # Get audio duration
            audio_duration = self.get_audio_duration(audio_path)
            if not audio_duration:
                logging.error("Could not determine audio duration")
                return None
            
            logging.info(f"Audio duration: {audio_duration:.2f} seconds")
            
            # Select B-roll video (any duration is fine since we'll loop if needed)
            broll_video = self.select_random_broll(0)  # Don't filter by duration
            if not broll_video:
                logging.error("Could not select suitable B-roll video")
                return None

            # Generate output filename
            post_summary = post.get('summary', 'reddit_post')
            output_filename = f"{post_summary}.mp4"
            output_path = self.output_dir / output_filename

            # Remove existing file if it exists
            if output_path.exists():
                output_path.unlink()

            logging.info(f"Creating video: {output_filename}")
            logging.info(f"B-roll: {broll_video.name}")

            # Get B-roll duration to determine looping strategy
            broll_duration = self.get_video_duration(str(broll_video))
            if not broll_duration:
                logging.error("Could not determine B-roll video duration")
                return None

            logging.info(f"B-roll duration: {broll_duration:.2f}s, Audio duration: {audio_duration:.2f}s")
            
            # Build ffmpeg command based on whether we need to loop or not
            if broll_duration >= audio_duration:
                # B-roll is longer than audio - use a random segment
                max_start_time = broll_duration - audio_duration
                start_time = random.uniform(0, max_start_time) if max_start_time > 0 else 0
                logging.info(f"Using B-roll segment starting at {start_time:.2f}s")

                cmd = [
                    'ffmpeg',
                    '-ss', str(start_time),
                    '-t', str(audio_duration),  # Limit video input to audio duration
                    '-i', str(broll_video),
                    '-i', audio_path,
                    '-filter_complex', f'[0:v]scale={self.video_settings["width"]}:{self.video_settings["height"]}:force_original_aspect_ratio=increase,crop={self.video_settings["width"]}:{self.video_settings["height"]},fps={self.video_settings["fps"]}[v]',
                    '-map', '[v]',
                    '-map', '1:a',
                    '-c:v', self.video_settings['video_codec'],
                    '-c:a', self.video_settings['audio_codec'],
                    '-b:a', self.video_settings['audio_bitrate'],
                    '-b:v', self.video_settings['video_bitrate'],
                    '-shortest',  # End when shortest stream ends (should be video since we trimmed it)
                    '-y',
                    str(output_path)
                ]
            else:
                # B-roll is shorter than audio - loop it to match audio duration
                loops_needed = int(audio_duration / broll_duration) + 1
                logging.info(f"Looping B-roll {loops_needed} times to match audio duration")

                cmd = [
                    'ffmpeg',
                    '-stream_loop', str(loops_needed - 1),  # Loop the video
                    '-i', str(broll_video),
                    '-i', audio_path,
                    '-filter_complex', f'[0:v]scale={self.video_settings["width"]}:{self.video_settings["height"]}:force_original_aspect_ratio=increase,crop={self.video_settings["width"]}:{self.video_settings["height"]},fps={self.video_settings["fps"]}[v]',
                    '-map', '[v]',
                    '-map', '1:a',
                    '-c:v', self.video_settings['video_codec'],
                    '-c:a', self.video_settings['audio_codec'],
                    '-b:a', self.video_settings['audio_bitrate'],
                    '-b:v', self.video_settings['video_bitrate'],
                    # No -t or -shortest here - let audio determine the final duration
                    '-y',
                    str(output_path)
                ]
            
            # Run ffmpeg
            subprocess.run(cmd, check=True, capture_output=True)
            
            # Verify output file was created and check duration
            if output_path.exists() and output_path.stat().st_size > 0:
                final_duration = self.get_video_duration(str(output_path))
                if final_duration:
                    duration_diff = abs(final_duration - audio_duration)
                    logging.info(f"Video created successfully: {output_path}")
                    logging.info(f"Final video duration: {final_duration:.2f}s (audio: {audio_duration:.2f}s)")

                    if duration_diff > 0.5:  # More than 0.5 second difference
                        logging.warning(f"Duration mismatch: video={final_duration:.2f}s, audio={audio_duration:.2f}s")
                    else:
                        logging.info("✅ Video duration matches audio duration")
                else:
                    logging.warning("Could not verify final video duration")

                return str(output_path)
            else:
                logging.error("Video file was not created or is empty")
                return None
            
        except Exception as e:
            logging.error(f"Error creating video for post {post.get('id', 'unknown')}: {e}")
            return None
    
    def create_videos_batch(self, posts: List[dict], audio_files: dict) -> dict:
        """
        Create videos for multiple posts.
        
        Args:
            posts: List of post dictionaries
            audio_files: Dictionary mapping post IDs to audio file paths
            
        Returns:
            Dictionary mapping post IDs to video file paths
        """
        video_files = {}
        
        for i, post in enumerate(posts):
            post_id = post['id']
            
            if post_id not in audio_files:
                logging.warning(f"No audio file for post {post_id}, skipping video creation")
                continue
            
            logging.info(f"Creating video {i+1}/{len(posts)}: {post['title'][:50]}...")
            
            video_path = self.create_video(post, audio_files[post_id])
            if video_path:
                video_files[post_id] = video_path
            else:
                logging.warning(f"Failed to create video for post {post_id}")
        
        logging.info(f"Created {len(video_files)}/{len(posts)} videos successfully")
        return video_files
    
    def add_subtitles(self, video_path: str, post: dict) -> Optional[str]:
        """
        Add subtitles to video (optional feature).
        
        Args:
            video_path: Path to video file
            post: Post dictionary with content
            
        Returns:
            Path to video with subtitles or None if failed
        """
        # This is a placeholder for subtitle functionality
        # Would require additional libraries like pysrt or subtitle generation
        logging.info("Subtitle generation not implemented yet")
        return video_path

def test_video_composer():
    """Test the video composer with sample files."""
    try:
        composer = VideoComposer()
        
        # Check for B-roll videos
        broll_videos = composer.get_available_broll_videos()
        print(f"Found {len(broll_videos)} B-roll videos:")
        for video in broll_videos:
            duration = composer.get_video_duration(str(video))
            print(f"  {video.name}: {duration:.2f}s" if duration else f"  {video.name}: unknown duration")
        
        if not broll_videos:
            print("No B-roll videos found. Please add some videos to the 'broll' directory.")
            return
        
        # Test with a sample audio file (if exists)
        audio_files = list(Path('temp_audio').glob('*.mp3'))
        if audio_files:
            test_audio = audio_files[0]
            print(f"\nTesting with audio file: {test_audio.name}")
            
            test_post = {
                'id': 'test1',
                'title': 'Test post for video creation',
                'summary': 'test_video'
            }
            
            video_path = composer.create_video(test_post, str(test_audio))
            if video_path:
                print(f"Test video created: {video_path}")
            else:
                print("Failed to create test video")
        else:
            print("No audio files found for testing. Run TTS generator first.")
            
    except Exception as e:
        print(f"Error testing video composer: {e}")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    test_video_composer()
