"""
Content filtering and moderation system for Reddit posts.
Handles NSFW filtering, profanity detection, and word count validation.
"""

import re
import math
from typing import Dict, Tuple
from better_profanity import profanity

class ContentFilter:
    def __init__(self):
        """Initialize the content filter with profanity detection."""
        profanity.load_censor_words()
        
        # Additional offensive terms to filter
        self.additional_blocklist = {
            'racial_slurs': [
                # Add specific terms as needed - keeping minimal for safety
            ],
            'extreme_profanity': [
            ],
            'nsfw_keywords': [
            ]
        }
        
        # Compile regex patterns for efficiency
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for filtering."""
        all_blocked = []
        for category in self.additional_blocklist.values():
            all_blocked.extend(category)
        
        # Create case-insensitive pattern
        pattern = r'\b(?:' + '|'.join(re.escape(word) for word in all_blocked) + r')\b'
        self.blocked_pattern = re.compile(pattern, re.IGNORECASE)
    
    def is_nsfw_content(self, title: str, content: str) -> bool:
        """Check if content contains NSFW material."""
        text = f"{title} {content}".lower()
        
        # Check for NSFW keywords
        for keyword in self.additional_blocklist['nsfw_keywords']:
            if keyword in text:
                return True
        
        return False
    
    def contains_extreme_profanity(self, title: str, content: str) -> bool:
        """Check if content contains extreme profanity or offensive language."""
        text = f"{title} {content}"
        
        # Use better-profanity library
        if profanity.contains_profanity(text):
            return True
        
        # Check our additional blocklist
        if self.blocked_pattern.search(text):
            return True
        
        return False
    
    def calculate_word_count_penalty(self, word_count: int) -> float:
        """
        Calculate penalty based on word count.
        Optimal range: 200-500 words
        """
        l, h = 200, 500
        if l <= word_count <= h:
            return 1.0  # No penalty
        
        if word_count < l:
            # Linear penalty for too short
            penalty = 0.7 + (word_count / l) * 0.2
            return max(0.7, penalty)
        
        if word_count > h:
            # Exponential penalty for too long
            excess = word_count - h
            penalty = 0.8 * math.exp(-excess / l)
            return max(0.3, penalty)
        
        return 1.0
    
    def get_word_count(self, text: str) -> int:
        """Get word count of text."""
        # Remove extra whitespace and count words
        words = text.strip().split()
        return len(words)
    
    def clean_text(self, text: str) -> str:
        """Clean text by removing excessive formatting and normalizing."""
        # Remove multiple newlines
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Remove excessive spaces
        text = re.sub(r' {2,}', ' ', text)
        
        # Remove markdown formatting that might interfere with TTS
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # Italic
        text = re.sub(r'~~(.*?)~~', r'\1', text)      # Strikethrough
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove Reddit-specific formatting
        text = re.sub(r'/u/\w+', '', text)  # Remove username mentions
        text = re.sub(r'/r/\w+', '', text)  # Remove subreddit mentions
        
        return text.strip()
    
    def filter_post(self, title: str, content: str) -> Dict:
        """
        Filter a Reddit post and return filtering results.
        
        Returns:
            Dict with keys: is_valid, reasons, word_count, length_penalty, cleaned_content
        """
        result = {
            'is_valid': True,
            'reasons': [],
            'word_count': 0,
            'length_penalty': 1.0,
            'cleaned_content': ''
        }
        
        # Clean the content first
        cleaned_content = self.clean_text(content)
        cleaned_title = self.clean_text(title)
        result['cleaned_content'] = cleaned_content
        
        # Calculate word count
        total_text = f"{cleaned_title} {cleaned_content}"
        word_count = self.get_word_count(total_text)
        result['word_count'] = word_count
        
        # Check for NSFW content
        if self.is_nsfw_content(title, content):
            result['is_valid'] = False
            result['reasons'].append('Contains NSFW content')
        
        # Check for extreme profanity
        if self.contains_extreme_profanity(title, content):
            result['is_valid'] = False
            result['reasons'].append('Contains extreme profanity or offensive language')
        
        # Calculate length penalty
        length_penalty = self.calculate_word_count_penalty(word_count)
        result['length_penalty'] = length_penalty
        
        # Add length warnings (not blocking, just penalty)
        if word_count < 75:
            result['reasons'].append(f'Post too short ({word_count} words, penalty: {length_penalty:.2f})')
        elif word_count > 300:
            result['reasons'].append(f'Post too long ({word_count} words, penalty: {length_penalty:.2f})')
        
        return result

def test_content_filter():
    """Test the content filter with sample posts."""
    filter_system = ContentFilter()
    
    # Test cases
    test_posts = [
        {
            'title': 'AITA for not sharing my cookies?',
            'content': 'My mother-in-law came over and demanded I give her all my homemade cookies. I said no because I made them for my kids. She got really upset and left. Now my husband is mad at me. Am I the asshole?'
        },
        {
            'title': 'NSFW: Adult content here',
            'content': 'This is explicit sexual content that should be filtered out.'
        },
        {
            'title': 'Short post',
            'content': 'Too short.'
        },
        {
            'title': 'Very long post that goes on and on',
            'content': 'This is a very long post that exceeds the word limit. ' * 50
        }
    ]
    
    for i, post in enumerate(test_posts):
        print(f"\nTest {i+1}:")
        print(f"Title: {post['title']}")
        result = filter_system.filter_post(post['title'], post['content'])
        print(f"Valid: {result['is_valid']}")
        print(f"Word count: {result['word_count']}")
        print(f"Length penalty: {result['length_penalty']:.2f}")
        print(f"Reasons: {result['reasons']}")

if __name__ == "__main__":
    test_content_filter()
