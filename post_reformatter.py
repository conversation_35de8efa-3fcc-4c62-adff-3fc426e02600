"""
Post reformatting system using OpenAI to optimize Reddit posts for audio storytelling.
"""

import os
import logging
import re
from typing import Dict, List, Optional
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class PostReformatter:
    def __init__(self):
        """Initialize OpenAI client for post reformatting."""
        self.client = None
        self._setup_openai_client()
        self._load_reformatting_prompt()
    
    def _setup_openai_client(self):
        """Set up OpenAI API client."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        
        self.client = OpenAI(api_key=api_key)
        logging.info("OpenAI reformatter client initialized successfully")
    
    def _load_reformatting_prompt(self):
        """Load the reformatting prompt template."""
        self.reformatting_prompt = """You are a content editor for Reddit posts. Your job is to make MINIMAL edits to clean up a Reddit post for text-to-speech narration.

IMPORTANT: Make VERY MINIMAL changes. Only do the following:

1. Fix obvious typos and spelling errors
2. Remove sections like "TL;DR:", "TLDR:", "Edit:", "UPDATE:", and similar meta-commentary
3. Remove or replace offensive language that might get content banned
4. Clean up excessive formatting (multiple line breaks, excessive punctuation)

DO NOT:
- Add introductions or hooks
- Reference TikTok, videos, or storytelling
- Change the tone or voice of the original author
- Add conclusions or questions
- Break up paragraphs unnecessarily
- Add dramatic language or emphasis
- Make the post sound like it's being told to an audience

The goal is to have a clean version of the original post that sounds natural when read aloud, while preserving the author's authentic voice and style.

# Original Post to Clean

Title: {title}
Content: {content}

# Cleaned Post:"""
    
    def reformat_post(self, post: Dict) -> Optional[str]:
        """
        Reformat a single post for audio storytelling optimization.
        
        Args:
            post: Post dictionary with title and content
            
        Returns:
            Reformatted content string or None if reformatting fails
        """
        try:
            # Prepare the prompt
            prompt = self.reformatting_prompt.format(
                title=post['title'],
                content=post.get('cleaned_content', post['content'])
            )
            
            logging.debug(f"Reformatting post: {post['title'][:50]}...")
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a content editor. Make MINIMAL edits to Reddit posts - only fix typos, remove TL;DR/Edit sections, and clean formatting. Do NOT add introductions, references to videos/TikTok, or change the author's voice. Preserve the original tone and style completely."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Lower temperature for more conservative edits
                max_tokens=1000
            )
            
            # Get the reformatted content
            reformatted_content = response.choices[0].message.content.strip()
            
            # Basic validation - ensure we got meaningful content back
            if len(reformatted_content) < 20:
                logging.warning(f"Reformatted content too short for post {post.get('id', 'unknown')}")
                return None
            
            # Remove any potential system messages or formatting artifacts
            reformatted_content = re.sub(r'^(Reformatted Post:|Here\'s the reformatted post:|# Reformatted Post)', '', reformatted_content, flags=re.IGNORECASE).strip()
            
            logging.debug(f"Successfully reformatted post {post.get('id', 'unknown')}")
            return reformatted_content
            
        except Exception as e:
            logging.error(f"Error reformatting post {post.get('id', 'unknown')}: {e}")
            return None
    
    def reformat_posts_batch(self, posts: List[Dict]) -> List[Dict]:
        """
        Reformat multiple posts for audio storytelling.
        
        Args:
            posts: List of post dictionaries
            
        Returns:
            List of posts with reformatted content
        """
        reformatted_posts = []
        
        for i, post in enumerate(posts):
            logging.info(f"Reformatting post {i+1}/{len(posts)}: {post['title'][:50]}...")
            
            reformatted_content = self.reformat_post(post)
            
            if reformatted_content:
                # Create a copy of the post with reformatted content
                reformatted_post = post.copy()
                reformatted_post['original_content'] = post.get('cleaned_content', post['content'])
                reformatted_post['cleaned_content'] = reformatted_content
                reformatted_post['reformatted'] = True
                
                # Update word count for the reformatted content
                word_count = len(reformatted_content.split())
                reformatted_post['word_count'] = word_count
                
                reformatted_posts.append(reformatted_post)
                logging.debug(f"✅ Reformatted: {word_count} words")
            else:
                # Keep original post if reformatting failed
                post['reformatted'] = False
                reformatted_posts.append(post)
                logging.warning(f"❌ Keeping original content for post {post.get('id', 'unknown')}")
        
        successful_reformats = sum(1 for p in reformatted_posts if p.get('reformatted', False))
        logging.info(f"Successfully reformatted {successful_reformats}/{len(posts)} posts")
        
        return reformatted_posts
    
    def preview_reformatting(self, post: Dict) -> Dict:
        """
        Preview reformatting changes for a single post.
        
        Args:
            post: Post dictionary
            
        Returns:
            Dictionary with original and reformatted content for comparison
        """
        original_content = post.get('cleaned_content', post['content'])
        reformatted_content = self.reformat_post(post)
        
        return {
            'title': post['title'],
            'original_content': original_content,
            'reformatted_content': reformatted_content,
            'original_word_count': len(original_content.split()),
            'reformatted_word_count': len(reformatted_content.split()) if reformatted_content else 0,
            'reformatting_successful': reformatted_content is not None
        }

def test_post_reformatter():
    """Test the post reformatter with sample posts."""
    try:
        reformatter = PostReformatter()
        
        # Sample posts for testing
        test_posts = [
            {
                'id': 'test1',
                'title': 'AITA for not sharing my cookies with my mother-in-law?',
                'content': 'So this happened yesterday. My mother-in-law came over unannounced (as usual) and saw that I had just finished baking a batch of chocolate chip cookies for my kids\' school bake sale tomorrow. She immediately demanded that I give her half of them because "family should share." I told her no because these were specifically for the school event and I had already promised them. She got really upset and started yelling about how I\'m selfish and don\'t care about family. My husband is now mad at me too. AITA?\n\nEdit: Thanks for all the responses! Just to clarify, she does this kind of thing all the time.\n\nTL;DR: MIL wanted my cookies, I said no, now everyone\'s mad.',
                'word_count': 120
            }
        ]
        
        print("Testing post reformatter...")
        
        for post in test_posts:
            print(f"\n{'='*60}")
            print(f"Original Title: {post['title']}")
            print(f"Original Content ({post['word_count']} words):")
            print(post['content'])
            
            preview = reformatter.preview_reformatting(post)
            
            print(f"\n{'='*60}")
            print(f"Reformatted Content ({preview['reformatted_word_count']} words):")
            if preview['reformatting_successful']:
                print(preview['reformatted_content'])
            else:
                print("❌ Reformatting failed")
            
    except Exception as e:
        print(f"Error testing post reformatter: {e}")
        print("Make sure you have set up your .env file with OpenAI API key")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    test_post_reformatter()
