"""
Text-to-Speech generation using OpenAI's TTS API.
"""

import os
import logging
from pathlib import Path
from typing import Optional
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TTSGenerator:
    def __init__(self):
        """Initialize OpenAI TTS client."""
        self.client = None
        self._setup_openai_client()
        
        # Available voices - using expressive ones as specified
        self.voices = {
            'onyx': 'Deep, expressive male voice',
            'echo': 'Warm, engaging male voice', 
            'fable': 'Expressive, storytelling voice',
            'nova': 'Energetic female voice',
            'shimmer': 'Warm female voice',
            'alloy': 'Balanced, clear voice'
        }
        
        # Default voice for engaging content
        self.default_voice = 'onyx'
        
        # Ensure temp_audio directory exists
        self.audio_dir = Path('temp_audio')
        self.audio_dir.mkdir(exist_ok=True)
    
    def _setup_openai_client(self):
        """Set up OpenAI API client."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        
        self.client = OpenAI(api_key=api_key)
        logging.info("OpenAI TTS client initialized successfully")
    
    def _prepare_narration_text(self, post: dict) -> str:
        """
        Prepare text for narration by adding natural speech elements.
        
        Args:
            post: Post dictionary with title and content
            
        Returns:
            Formatted text ready for TTS
        """
        title = post['title']
        content = post.get('cleaned_content', post['content'])
        
        # Create engaging narration
        narration_parts = []
        
        # Add engaging intro
        if 'AITA' in title.upper():
            narration_parts.append("Here's a wild story from Reddit's Am I The Asshole.")
        elif 'TIFU' in title.upper():
            narration_parts.append("Today I F'd up! Here's what happened.")
        else:
            narration_parts.append("Here's an interesting story from Reddit.")
        
        # Add the title (clean it up for speech)
        clean_title = title.replace('AITA', 'Am I the asshole')
        clean_title = clean_title.replace('TIFU', 'Today I messed up')
        clean_title = clean_title.replace('WIBTA', 'Would I be the asshole')
        
        narration_parts.append(clean_title)
        
        # Add a transition
        narration_parts.append("Here's the full story:")
        
        # Add the content
        narration_parts.append(content)
        
        # Join with appropriate pauses
        full_narration = ' ... '.join(narration_parts)
        
        # Clean up for better speech
        full_narration = full_narration.replace('&amp;', 'and')
        full_narration = full_narration.replace('&lt;', 'less than')
        full_narration = full_narration.replace('&gt;', 'greater than')
        full_narration = full_narration.replace('TL;DR', 'Too long, didn\'t read')
        full_narration = full_narration.replace('TLDR', 'Too long, didn\'t read')
        
        # Add natural pauses for better pacing
        full_narration = full_narration.replace('. ', '. ... ')
        full_narration = full_narration.replace('? ', '? ... ')
        full_narration = full_narration.replace('! ', '! ... ')
        
        return full_narration
    
    def generate_audio(self, post: dict, voice: Optional[str] = None) -> Optional[str]:
        """
        Generate audio narration for a post.
        
        Args:
            post: Post dictionary with title and content
            voice: Voice to use (defaults to self.default_voice)
            
        Returns:
            Path to generated audio file or None if failed
        """
        if not voice:
            voice = self.default_voice
        
        if voice not in self.voices:
            logging.warning(f"Unknown voice '{voice}', using default '{self.default_voice}'")
            voice = self.default_voice
        
        try:
            # Prepare narration text
            narration_text = self._prepare_narration_text(post)
            
            # Generate filename
            post_summary = post.get('summary', 'reddit_post')
            audio_filename = f"{post_summary}_audio.mp3"
            audio_path = self.audio_dir / audio_filename
            
            logging.info(f"Generating TTS audio for post: {post['title'][:50]}...")
            logging.info(f"Using voice: {voice}")
            logging.info(f"Text length: {len(narration_text)} characters")
            
            # Call OpenAI TTS API
            response = self.client.audio.speech.create(
                model="tts-1",
                voice=voice,
                input=narration_text,
                speed=1.1  # Slightly faster for engaging content
            )
            
            # Save audio to file
            with open(audio_path, 'wb') as f:
                f.write(response.content)
            
            logging.info(f"Audio generated successfully: {audio_path}")
            return str(audio_path)
            
        except Exception as e:
            logging.error(f"Error generating TTS audio for post {post.get('id', 'unknown')}: {e}")
            return None
    
    def generate_audio_batch(self, posts: list, voice: Optional[str] = None) -> dict:
        """
        Generate audio for multiple posts.
        
        Args:
            posts: List of post dictionaries
            voice: Voice to use for all posts
            
        Returns:
            Dictionary mapping post IDs to audio file paths
        """
        audio_files = {}
        
        for i, post in enumerate(posts):
            logging.info(f"Generating audio {i+1}/{len(posts)}")
            
            audio_path = self.generate_audio(post, voice)
            if audio_path:
                audio_files[post['id']] = audio_path
            else:
                logging.warning(f"Failed to generate audio for post {post['id']}")
        
        logging.info(f"Generated audio for {len(audio_files)}/{len(posts)} posts")
        return audio_files
    
    def get_audio_duration(self, audio_path: str) -> Optional[float]:
        """
        Get duration of audio file in seconds.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Duration in seconds or None if failed
        """
        try:
            import mutagen
            from mutagen.mp3 import MP3
            
            audio = MP3(audio_path)
            return audio.info.length
            
        except ImportError:
            logging.warning("mutagen not installed, cannot get audio duration")
            return None
        except Exception as e:
            logging.error(f"Error getting audio duration for {audio_path}: {e}")
            return None
    
    def cleanup_temp_audio(self):
        """Clean up temporary audio files."""
        try:
            for audio_file in self.audio_dir.glob('*.mp3'):
                audio_file.unlink()
            logging.info("Cleaned up temporary audio files")
        except Exception as e:
            logging.error(f"Error cleaning up audio files: {e}")

def test_tts_generator():
    """Test the TTS generator with a sample post."""
    try:
        tts = TTSGenerator()
        
        # Sample post for testing
        test_post = {
            'id': 'test1',
            'title': 'AITA for not sharing my cookies with my mother-in-law?',
            'content': 'My mother-in-law came over and demanded I give her all my homemade cookies. I said no because I made them for my kids. She got really upset and left. Now my husband is mad at me. What do you think?',
            'summary': 'mother_cookie_reject'
        }
        
        print("Available voices:")
        for voice, description in tts.voices.items():
            print(f"  {voice}: {description}")
        
        print(f"\nGenerating audio with voice '{tts.default_voice}'...")
        audio_path = tts.generate_audio(test_post)
        
        if audio_path:
            print(f"Audio generated successfully: {audio_path}")
            
            # Try to get duration if possible
            duration = tts.get_audio_duration(audio_path)
            if duration:
                print(f"Audio duration: {duration:.2f} seconds")
        else:
            print("Failed to generate audio")
            
    except Exception as e:
        print(f"Error testing TTS generator: {e}")
        print("Make sure you have set up your .env file with OpenAI API key")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    test_tts_generator()
