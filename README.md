# Reddit2ShortsCLI

A Python CLI tool that generates TikTok-ready short videos from Reddit posts. The tool fetches posts from a specified subreddit, grades them using AI, generates audio narration, and creates engaging short videos with gameplay footage.

## Features

- 🔍 **Smart Post Fetching**: Uses Reddit API to fetch and filter high-quality posts
- 🤖 **AI-Powered Grading**: Uses OpenAI GPT-4o to score posts on humor, controversy, and TikTok engagement potential
- 🎙️ **Professional Narration**: Generates expressive audio using OpenAI's TTS API
- 🎬 **Automated Video Creation**: Combines narration with gameplay footage using ffmpeg
- 🛡️ **Content Safety**: Built-in filtering for NSFW content and inappropriate language
- ⚡ **Fast Processing**: Generates videos in under 3 minutes each

## Installation

1. **Clone or download this repository**

2. **Install Python dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

3. **Install ffmpeg** (required for video processing):

   - **macOS**: `brew install ffmpeg`
   - **Ubuntu/Debian**: `sudo apt install ffmpeg`
   - **Windows**: Download from https://ffmpeg.org/download.html

4. **Set up API credentials**:

   - Copy `.env.template` to `.env`
   - Fill in your API credentials:
     ```
     REDDIT_CLIENT_ID=your_reddit_client_id
     REDDIT_CLIENT_SECRET=your_reddit_client_secret
     REDDIT_USER_AGENT=Reddit2ShortsCLI/1.0
     OPENAI_API_KEY=your_openai_api_key
     ```

5. **Add B-roll videos**:
   - Create a `broll` directory
   - Add gameplay videos (MP4 format recommended)
   - Videos should be at least 1-2 minutes long
   - Suggested content: Minecraft parkour, Subway Surfers, mobile games

## API Setup

### Reddit API

1. Go to https://www.reddit.com/prefs/apps
2. Click "Create App" or "Create Another App"
3. Choose "script" as the app type
4. Note down the client ID and secret

### OpenAI API

1. Go to https://platform.openai.com/api-keys
2. Create a new API key
3. Make sure you have credits in your account

## Usage

Basic usage:

```bash
python reddit2shorts.py --subreddit AITA --limit 50 --output 3
```

### Command Line Options

- `--subreddit`: Name of the subreddit to scrape from (required)
- `--limit`: Number of posts to fetch (1-100, default: 50)
- `--output`: Number of videos to generate (1-10, default: 3)
- `--voice`: TTS voice to use (onyx, echo, fable, nova, shimmer, alloy)
- `--verbose`: Enable detailed logging

### Examples

Generate 5 videos from r/AmItheAsshole:

```bash
python reddit2shorts.py --subreddit AmItheAsshole --limit 100 --output 5
```

Use a different voice:

```bash
python reddit2shorts.py --subreddit tifu --limit 50 --output 3 --voice echo
```

Enable verbose logging:

```bash
python reddit2shorts.py --subreddit relationship_advice --limit 30 --output 2 --verbose
```

## How It Works

1. **Fetch Posts**: Retrieves top posts from the specified subreddit using Reddit API
2. **Content Filtering**: Removes NSFW content, excessive profanity, and posts with poor word counts
3. **Post Reformatting**: Uses AI to optimize posts for audio storytelling by fixing typos, improving flow, and enhancing engagement
4. **AI Grading**: Each post is scored by GPT-4o on:
   - Storytelling quality (0-20)
   - Engagement potential (0-20)
   - Viral likelihood (0-20)
   - Content uniqueness (0-20)
   - Execution flexibility (0-20)
5. **Post Selection**: Top-scoring posts are selected for video generation
6. **Audio Generation**: Creates engaging narration using OpenAI TTS
7. **Video Composition**: Combines narration with random gameplay footage
8. **Output**: Saves TikTok-ready videos (1080x1920, 30fps) to `output_videos/`

## Directory Structure

```
reddit2shorts/
├── reddit2shorts.py          # Main CLI script
├── reddit_fetcher.py         # Reddit API integration
├── post_reformatter.py       # AI post reformatting for audio storytelling
├── post_grader.py            # AI post grading system
├── tts_generator.py          # Text-to-speech generation
├── video_composer.py         # Video creation pipeline
├── content_filter.py         # Content filtering and moderation
├── requirements.txt          # Python dependencies
├── .env                      # API credentials (create from template)
├── broll/                    # Gameplay videos for background
│   ├── minecraft1.mp4
│   └── subway1.mp4
├── output_videos/            # Generated videos
├── temp_audio/               # Temporary audio files
└── prompts/
    └── grading_prompt.txt    # AI grading prompt template
```

## Troubleshooting

### Common Issues

1. **"Reddit API credentials not found"**

   - Make sure you've created a `.env` file with valid Reddit API credentials

2. **"OpenAI API key not found"**

   - Ensure your OpenAI API key is set in the `.env` file
   - Verify you have credits in your OpenAI account

3. **"No B-roll videos found"**

   - Add MP4 videos to the `broll/` directory
   - Ensure videos are at least 1-2 minutes long

4. **"ffmpeg not found"**

   - Install ffmpeg using your system's package manager

5. **Videos are too short/long**
   - The tool automatically adjusts based on narration length
   - Ensure your B-roll videos are longer than typical narrations (1-2 minutes)

### Performance Tips

- Use SSD storage for faster video processing
- Ensure stable internet connection for API calls
- Consider using shorter post limits for faster processing
- Monitor your OpenAI API usage to avoid rate limits

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is for educational purposes. Please respect Reddit's API terms of service and OpenAI's usage policies.
