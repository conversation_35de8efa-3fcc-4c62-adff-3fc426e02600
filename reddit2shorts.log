2025-06-18 23:52:56,835 - INFO - Initializing Reddit2Shorts components...
2025-06-18 23:52:56,842 - INFO - Reddit API client initialized successfully
2025-06-18 23:52:56,901 - INFO - OpenAI client initialized successfully
2025-06-18 23:52:56,914 - INFO - OpenAI TTS client initialized successfully
2025-06-18 23:52:56,914 - INFO - All components initialized successfully
2025-06-18 23:52:56,915 - INFO - Starting Reddit2Shorts pipeline:
2025-06-18 23:52:56,915 - INFO -   Subreddit: r/amitheasshole
2025-06-18 23:52:56,915 - INFO -   Posts to fetch: 20
2025-06-18 23:52:56,915 - INFO -   Videos to generate: 1
2025-06-18 23:52:56,915 - INFO -   TTS Voice: onyx
2025-06-18 23:52:56,915 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-18 23:52:56,916 - INFO - Fetching top 20 posts from r/amitheasshole
2025-06-18 23:52:57,754 - INFO - Successfully fetched 19 posts
2025-06-18 23:53:17,368 - INFO - Filtered 19 posts down to 12 valid posts
2025-06-18 23:53:17,368 - INFO - Found 12 valid posts after filtering
2025-06-18 23:53:17,368 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-18 23:53:17,368 - INFO - Grading post 1/12: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:53:22,096 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:22,110 - INFO - Grading post 2/12: AITA for moving into a studio apartment away from ...
2025-06-18 23:53:26,535 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:26,541 - INFO - Grading post 3/12: AITA for telling my dad I felt excluded after his ...
2025-06-18 23:53:30,902 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:30,904 - INFO - Grading post 4/12: AITA for telling my mom that I don't care if she l...
2025-06-18 23:53:36,330 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:36,331 - INFO - Grading post 5/12: AITA for having lunch in a cemetery?...
2025-06-18 23:53:41,603 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:41,606 - INFO - Grading post 6/12: AITA for cutting my honeymoon short to be here for...
2025-06-18 23:53:46,572 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:46,581 - INFO - Grading post 7/12: AITA for turning down a weird solicitor in a way m...
2025-06-18 23:53:51,074 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:51,076 - INFO - Grading post 8/12: AITA for standing my ground and keeping my baby ev...
2025-06-18 23:53:55,274 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:55,276 - INFO - Grading post 9/12: AITA for asking my housemate to pay more for utili...
2025-06-18 23:53:59,543 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:59,546 - INFO - Grading post 10/12: WIBTA if I paid my SIL what I think she should get...
2025-06-18 23:54:04,051 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:04,053 - INFO - Grading post 11/12: AITA for making my husbands friends cancel a holid...
2025-06-18 23:54:08,079 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:08,082 - INFO - Grading post 12/12: AITA for not cleaning after I finished house/pet s...
2025-06-18 23:54:11,665 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:11,668 - INFO - Successfully graded 12/12 posts
2025-06-18 23:54:11,668 - INFO - Selected top 1 posts for video generation:
2025-06-18 23:54:11,668 - INFO -   1. AITA for asking my boyfriend to contribute to groc... (score: 5.20)
2025-06-18 23:54:11,669 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-18 23:54:11,669 - INFO - Generating audio 1/1
2025-06-18 23:54:11,669 - INFO - Generating TTS audio for post: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:54:11,669 - INFO - Using voice: onyx
2025-06-18 23:54:11,669 - INFO - Text length: 1218 characters
2025-06-18 23:54:13,615 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-18 23:54:24,706 - INFO - Audio generated successfully: temp_audio/aitagroceriesboyfriend_audio.mp3
2025-06-18 23:54:24,706 - INFO - Generated audio for 1/1 posts
2025-06-18 23:54:24,706 - INFO - 
=== Step 4: Creating videos ===
2025-06-18 23:54:24,706 - INFO - Creating video 1/1: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:54:24,706 - ERROR - Error getting audio duration for temp_audio/aitagroceriesboyfriend_audio.mp3: module 'ffmpeg' has no attribute 'probe'
2025-06-18 23:54:24,706 - ERROR - Could not determine audio duration
2025-06-18 23:54:24,706 - WARNING - Failed to create video for post 1lezl8q
2025-06-18 23:54:24,706 - INFO - Created 0/1 videos successfully
2025-06-18 23:54:24,706 - ERROR - No videos created
2025-06-19 11:18:27,101 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:18:27,231 - INFO - Reddit API client initialized successfully
2025-06-19 11:18:27,293 - INFO - OpenAI client initialized successfully
2025-06-19 11:18:27,302 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:18:27,302 - INFO - All components initialized successfully
2025-06-19 11:18:27,303 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:18:27,303 - INFO -   Subreddit: r/tifu
2025-06-19 11:18:27,303 - INFO -   Posts to fetch: 50
2025-06-19 11:18:27,303 - INFO -   Videos to generate: 3
2025-06-19 11:18:27,303 - INFO -   TTS Voice: echo
2025-06-19 11:18:27,303 - INFO -   Resume mode: False
2025-06-19 11:18:27,303 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:18:27,303 - INFO - Fetching top 50 posts from r/tifu
2025-06-19 11:18:28,721 - INFO - Successfully fetched 50 posts
2025-06-19 11:18:37,587 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:18:37,594 - INFO - Reddit API client initialized successfully
2025-06-19 11:18:37,620 - INFO - OpenAI client initialized successfully
2025-06-19 11:18:37,626 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:18:37,627 - INFO - All components initialized successfully
2025-06-19 11:18:37,627 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:18:37,627 - INFO -   Subreddit: r/tifu
2025-06-19 11:18:37,627 - INFO -   Posts to fetch: 3
2025-06-19 11:18:37,627 - INFO -   Videos to generate: 1
2025-06-19 11:18:37,627 - INFO -   TTS Voice: echo
2025-06-19 11:18:37,627 - INFO -   Resume mode: False
2025-06-19 11:18:37,628 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:18:37,628 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:18:38,151 - INFO - Successfully fetched 3 posts
2025-06-19 11:18:39,860 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:18:39,861 - INFO - Found 1 valid posts after filtering
2025-06-19 11:18:39,861 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:18:39,861 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:18:44,177 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:18:44,194 - INFO - Successfully graded 1/1 posts
2025-06-19 11:18:44,194 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:18:44,194 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 4.80)
2025-06-19 11:18:44,194 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:18:44,194 - INFO - Generating audio 1/1
2025-06-19 11:18:44,194 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:18:44,194 - INFO - Using voice: echo
2025-06-19 11:18:44,195 - INFO - Text length: 1166 characters
2025-06-19 11:18:46,208 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:18:53,955 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:18:53,955 - INFO - Generated audio for 1/1 posts
2025-06-19 11:18:53,955 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:18:53,955 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:18:53,982 - INFO - Audio duration: 63.58 seconds
2025-06-19 11:18:53,982 - INFO - Found 1 B-roll videos
2025-06-19 11:18:53,982 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:18:53,983 - WARNING - No B-roll videos long enough for 63.58s, using longest available
2025-06-19 11:18:53,983 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:18:53,983 - ERROR - Could not select suitable B-roll video
2025-06-19 11:18:53,983 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:18:53,983 - INFO - Created 0/1 videos successfully
2025-06-19 11:18:53,983 - ERROR - No videos created
2025-06-19 11:20:28,521 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:20:28,528 - INFO - Reddit API client initialized successfully
2025-06-19 11:20:28,555 - INFO - OpenAI client initialized successfully
2025-06-19 11:20:28,562 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:20:28,562 - INFO - All components initialized successfully
2025-06-19 11:20:28,563 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:20:28,563 - INFO -   Subreddit: r/tifu
2025-06-19 11:20:28,563 - INFO -   Posts to fetch: 3
2025-06-19 11:20:28,563 - INFO -   Videos to generate: 1
2025-06-19 11:20:28,563 - INFO -   TTS Voice: echo
2025-06-19 11:20:28,563 - INFO -   Resume mode: False
2025-06-19 11:20:28,563 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:20:28,563 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:20:29,139 - INFO - Successfully fetched 3 posts
2025-06-19 11:20:30,831 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:20:30,831 - INFO - Found 1 valid posts after filtering
2025-06-19 11:20:30,831 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:20:30,831 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:20:35,129 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:20:35,140 - INFO - Successfully graded 1/1 posts
2025-06-19 11:20:35,141 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:20:35,141 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 4.80)
2025-06-19 11:20:35,141 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:20:35,141 - INFO - Generating audio 1/1
2025-06-19 11:20:35,141 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:20:35,141 - INFO - Using voice: echo
2025-06-19 11:20:35,141 - INFO - Text length: 1166 characters
2025-06-19 11:20:37,938 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:20:49,453 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:20:49,453 - INFO - Generated audio for 1/1 posts
2025-06-19 11:20:49,453 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:20:49,454 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:20:49,466 - INFO - Audio duration: 63.00 seconds
2025-06-19 11:20:49,467 - INFO - Found 1 B-roll videos
2025-06-19 11:20:49,467 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:20:49,467 - WARNING - No B-roll videos long enough for 63.00s, using longest available
2025-06-19 11:20:49,467 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:20:49,467 - ERROR - Could not select suitable B-roll video
2025-06-19 11:20:49,467 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:20:49,467 - INFO - Created 0/1 videos successfully
2025-06-19 11:20:49,467 - ERROR - No videos created
2025-06-19 11:22:13,447 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:22:13,454 - INFO - Reddit API client initialized successfully
2025-06-19 11:22:13,480 - INFO - OpenAI client initialized successfully
2025-06-19 11:22:13,488 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:22:13,488 - INFO - All components initialized successfully
2025-06-19 11:22:13,489 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:22:13,489 - INFO -   Subreddit: r/tifu
2025-06-19 11:22:13,489 - INFO -   Posts to fetch: 3
2025-06-19 11:22:13,489 - INFO -   Videos to generate: 1
2025-06-19 11:22:13,489 - INFO -   TTS Voice: echo
2025-06-19 11:22:13,489 - INFO -   Resume mode: False
2025-06-19 11:22:13,489 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:22:13,490 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:22:14,055 - INFO - Successfully fetched 3 posts
2025-06-19 11:22:15,754 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:22:15,754 - INFO - Found 1 valid posts after filtering
2025-06-19 11:22:15,754 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:22:15,754 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:22:20,908 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:22:20,920 - INFO - Successfully graded 1/1 posts
2025-06-19 11:22:20,920 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:22:20,920 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 5.10)
2025-06-19 11:22:20,920 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:22:20,920 - INFO - Generating audio 1/1
2025-06-19 11:22:20,920 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:22:20,921 - INFO - Using voice: echo
2025-06-19 11:22:20,921 - INFO - Text length: 1166 characters
2025-06-19 11:22:22,381 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:22:29,842 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:22:29,843 - INFO - Generated audio for 1/1 posts
2025-06-19 11:22:29,843 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:22:29,843 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:22:29,855 - INFO - Audio duration: 63.12 seconds
2025-06-19 11:22:29,856 - INFO - Found 1 B-roll videos
2025-06-19 11:22:29,856 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:22:29,856 - WARNING - No B-roll videos long enough for 63.12s, using longest available
2025-06-19 11:22:29,856 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:22:29,856 - ERROR - Could not select suitable B-roll video
2025-06-19 11:22:29,856 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:22:29,856 - INFO - Created 0/1 videos successfully
2025-06-19 11:22:29,856 - ERROR - No videos created
2025-06-19 11:23:48,490 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:23:48,500 - INFO - Reddit API client initialized successfully
2025-06-19 11:23:48,535 - INFO - OpenAI client initialized successfully
2025-06-19 11:23:48,545 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:23:48,545 - INFO - All components initialized successfully
2025-06-19 11:23:48,545 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:23:48,546 - INFO -   Subreddit: r/tifu
2025-06-19 11:23:48,546 - INFO -   Posts to fetch: 3
2025-06-19 11:23:48,546 - INFO -   Videos to generate: 1
2025-06-19 11:23:48,546 - INFO -   TTS Voice: echo
2025-06-19 11:23:48,546 - INFO -   Resume mode: False
2025-06-19 11:23:48,546 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:23:48,546 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:23:49,071 - INFO - Successfully fetched 3 posts
2025-06-19 11:23:50,793 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:23:50,793 - INFO - Found 1 valid posts after filtering
2025-06-19 11:23:50,793 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:23:50,793 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:23:55,381 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:23:55,392 - INFO - Successfully graded 1/1 posts
2025-06-19 11:23:55,392 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:23:55,393 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 5.10)
2025-06-19 11:23:55,393 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:23:55,393 - INFO - Generating audio 1/1
2025-06-19 11:23:55,393 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:23:55,393 - INFO - Using voice: echo
2025-06-19 11:23:55,393 - INFO - Text length: 1166 characters
2025-06-19 11:23:57,750 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:24:11,147 - INFO - Audio generated successfully: temp_audio/deafadvicefail_audio.mp3
2025-06-19 11:24:11,147 - INFO - Generated audio for 1/1 posts
2025-06-19 11:24:11,147 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:24:11,147 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:24:11,159 - INFO - Audio duration: 63.48 seconds
2025-06-19 11:24:11,160 - INFO - Found 1 B-roll videos
2025-06-19 11:24:11,160 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:24:11,160 - WARNING - No B-roll videos long enough for 63.48s, using longest available
2025-06-19 11:24:11,160 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:24:11,160 - ERROR - Could not select suitable B-roll video
2025-06-19 11:24:11,160 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:24:11,160 - INFO - Created 0/1 videos successfully
2025-06-19 11:24:11,160 - ERROR - No videos created
2025-06-19 11:24:33,605 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:24:33,611 - INFO - Reddit API client initialized successfully
2025-06-19 11:24:33,637 - INFO - OpenAI client initialized successfully
2025-06-19 11:24:33,645 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:24:33,645 - INFO - All components initialized successfully
2025-06-19 11:24:33,645 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:24:33,645 - INFO -   Subreddit: r/tifu
2025-06-19 11:24:33,645 - INFO -   Posts to fetch: 3
2025-06-19 11:24:33,645 - INFO -   Videos to generate: 1
2025-06-19 11:24:33,646 - INFO -   TTS Voice: echo
2025-06-19 11:24:33,646 - INFO -   Resume mode: False
2025-06-19 11:24:33,646 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:24:33,646 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:24:34,175 - INFO - Successfully fetched 3 posts
2025-06-19 11:24:35,901 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:24:35,901 - INFO - Found 1 valid posts after filtering
2025-06-19 11:24:35,901 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:24:35,901 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:24:39,918 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:24:39,929 - INFO - Successfully graded 1/1 posts
2025-06-19 11:24:39,930 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:24:39,930 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 5.50)
2025-06-19 11:24:39,930 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:24:39,930 - INFO - Generating audio 1/1
2025-06-19 11:24:39,930 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:24:39,930 - INFO - Using voice: echo
2025-06-19 11:24:39,930 - INFO - Text length: 1166 characters
2025-06-19 11:24:42,559 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:24:48,476 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:24:48,476 - INFO - Generated audio for 1/1 posts
2025-06-19 11:24:48,476 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:24:48,476 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:24:48,488 - INFO - Audio duration: 63.12 seconds
2025-06-19 11:24:48,489 - INFO - Found 1 B-roll videos
2025-06-19 11:24:51,233 - WARNING - No B-roll videos long enough for 63.12s, using longest available
2025-06-19 11:24:51,480 - INFO - Creating video: deafaudiobookadvice.mp4
2025-06-19 11:24:51,480 - INFO - B-roll: minecraft_parkour.mp4
2025-06-19 11:24:51,701 - ERROR - Error creating video for post 1lez58m: module 'ffmpeg' has no attribute 'input'
2025-06-19 11:24:51,701 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:24:51,701 - INFO - Created 0/1 videos successfully
2025-06-19 11:24:51,701 - ERROR - No videos created
2025-06-19 11:26:26,289 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:26:26,297 - INFO - Reddit API client initialized successfully
2025-06-19 11:26:26,336 - INFO - OpenAI client initialized successfully
2025-06-19 11:26:26,345 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:26:26,345 - INFO - All components initialized successfully
2025-06-19 11:26:26,346 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:26:26,346 - INFO -   Subreddit: r/tifu
2025-06-19 11:26:26,346 - INFO -   Posts to fetch: 3
2025-06-19 11:26:26,346 - INFO -   Videos to generate: 1
2025-06-19 11:26:26,346 - INFO -   TTS Voice: onyx
2025-06-19 11:26:26,346 - INFO -   Resume mode: True
2025-06-19 11:26:26,346 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:26:26,346 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:26:27,067 - INFO - Successfully fetched 3 posts
2025-06-19 11:26:28,762 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:26:28,763 - INFO - Found 1 valid posts after filtering
2025-06-19 11:26:28,763 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:26:28,763 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:26:32,745 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:26:32,756 - INFO - Successfully graded 1/1 posts
2025-06-19 11:26:32,757 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:26:32,757 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 4.80)
2025-06-19 11:26:32,757 - INFO - 
=== Step 3: Checking for existing audio files ===
2025-06-19 11:26:32,757 - INFO - Found 3 existing audio files
2025-06-19 11:26:32,757 - INFO - Existing audio files:
2025-06-19 11:26:32,758 - INFO -   - aitagroceriesboyfriend: aitagroceriesboyfriend_audio.mp3
2025-06-19 11:26:32,758 - INFO -   - deafaudiobookadvice: deafaudiobookadvice_audio.mp3
2025-06-19 11:26:32,758 - INFO -   - deafadvicefail: deafadvicefail_audio.mp3
2025-06-19 11:26:32,758 - INFO - ✅ Found audio for: TIFU by giving a colleague sound advice....
2025-06-19 11:26:32,758 - INFO - 🔄 Reusing 1 existing audio files
2025-06-19 11:26:32,758 - INFO - ✅ Using 1 total audio files (1 reused + 0 generated)
2025-06-19 11:26:32,758 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:26:32,758 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:26:32,771 - INFO - Audio duration: 63.12 seconds
2025-06-19 11:26:32,772 - INFO - Found 1 B-roll videos
2025-06-19 11:26:33,563 - WARNING - No B-roll videos long enough for 63.12s, using longest available
2025-06-19 11:26:33,800 - INFO - Creating video: deafaudiobookadvice.mp4
2025-06-19 11:26:33,800 - INFO - B-roll: minecraft_parkour.mp4
2025-06-19 11:27:22,843 - INFO - Video created successfully: output_videos/deafaudiobookadvice.mp4
2025-06-19 11:27:22,844 - INFO - Created 1/1 videos successfully
2025-06-19 11:27:22,844 - INFO - 
=== Pipeline completed successfully! ===
2025-06-19 11:27:22,844 - INFO - Generated 1 videos in 56.50 seconds
2025-06-19 11:27:22,845 - INFO - Videos saved to: output_videos
2025-06-19 11:27:22,845 - INFO -   📹 deafaudiobookadvice.mp4 - TIFU by giving a colleague sound advice....
2025-06-19 11:27:22,845 - INFO - 💾 Audio files preserved for future resume operations
2025-06-19 11:48:54,840 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:48:54,849 - INFO - Reddit API client initialized successfully
2025-06-19 11:48:54,904 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:48:54,914 - INFO - OpenAI client initialized successfully
2025-06-19 11:48:54,927 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:48:54,927 - INFO - All components initialized successfully
2025-06-19 11:48:54,928 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:48:54,928 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:48:54,928 - INFO -   Posts to fetch: 10
2025-06-19 11:48:54,928 - INFO -   Videos to generate: 2
2025-06-19 11:48:54,928 - INFO -   TTS Voice: alloy
2025-06-19 11:48:54,928 - INFO -   Resume mode: False
2025-06-19 11:48:54,928 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:48:54,928 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:48:55,713 - INFO - Successfully fetched 9 posts
2025-06-19 11:49:05,379 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:49:05,380 - ERROR - No valid posts found after filtering
2025-06-19 11:51:20,463 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:51:20,471 - INFO - Reddit API client initialized successfully
2025-06-19 11:51:20,518 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:51:20,526 - INFO - OpenAI client initialized successfully
2025-06-19 11:51:20,534 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:51:20,534 - INFO - All components initialized successfully
2025-06-19 11:51:20,534 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:51:20,534 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:51:20,534 - INFO -   Posts to fetch: 10
2025-06-19 11:51:20,534 - INFO -   Videos to generate: 2
2025-06-19 11:51:20,534 - INFO -   TTS Voice: alloy
2025-06-19 11:51:20,535 - INFO -   Resume mode: False
2025-06-19 11:51:20,535 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:51:20,535 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:51:21,300 - INFO - Successfully fetched 9 posts
2025-06-19 11:51:21,303 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:51:21,303 - ERROR - No valid posts found after filtering
2025-06-19 11:52:14,769 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:52:14,776 - INFO - Reddit API client initialized successfully
2025-06-19 11:52:14,835 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:52:14,868 - INFO - OpenAI client initialized successfully
2025-06-19 11:52:14,880 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:52:14,881 - INFO - All components initialized successfully
2025-06-19 11:52:14,882 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:52:14,882 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:52:14,882 - INFO -   Posts to fetch: 10
2025-06-19 11:52:14,882 - INFO -   Videos to generate: 2
2025-06-19 11:52:14,882 - INFO -   TTS Voice: alloy
2025-06-19 11:52:14,882 - INFO -   Resume mode: False
2025-06-19 11:52:14,882 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:52:14,882 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:52:15,620 - INFO - Successfully fetched 9 posts
2025-06-19 11:52:15,624 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:52:15,624 - ERROR - No valid posts found after filtering
2025-06-19 11:52:39,574 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:52:39,581 - INFO - Reddit API client initialized successfully
2025-06-19 11:52:39,626 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:52:39,634 - INFO - OpenAI client initialized successfully
2025-06-19 11:52:39,642 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:52:39,643 - INFO - All components initialized successfully
2025-06-19 11:52:39,643 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:52:39,643 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:52:39,643 - INFO -   Posts to fetch: 10
2025-06-19 11:52:39,643 - INFO -   Videos to generate: 2
2025-06-19 11:52:39,643 - INFO -   TTS Voice: alloy
2025-06-19 11:52:39,643 - INFO -   Resume mode: False
2025-06-19 11:52:39,643 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:52:39,643 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:52:40,362 - INFO - Successfully fetched 9 posts
2025-06-19 11:52:40,363 - ERROR - Pipeline failed: name 'logging' is not defined
2025-06-19 11:52:51,840 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:52:51,847 - INFO - Reddit API client initialized successfully
2025-06-19 11:52:51,887 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:52:51,897 - INFO - OpenAI client initialized successfully
2025-06-19 11:52:51,906 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:52:51,906 - INFO - All components initialized successfully
2025-06-19 11:52:51,907 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:52:51,907 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:52:51,907 - INFO -   Posts to fetch: 10
2025-06-19 11:52:51,907 - INFO -   Videos to generate: 2
2025-06-19 11:52:51,907 - INFO -   TTS Voice: alloy
2025-06-19 11:52:51,907 - INFO -   Resume mode: False
2025-06-19 11:52:51,907 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:52:51,907 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:52:52,574 - INFO - Successfully fetched 9 posts
2025-06-19 11:52:52,578 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:52:52,578 - ERROR - No valid posts found after filtering
2025-06-19 11:53:13,518 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:53:13,525 - INFO - Reddit API client initialized successfully
2025-06-19 11:53:13,565 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:53:13,574 - INFO - OpenAI client initialized successfully
2025-06-19 11:53:13,583 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:53:13,583 - INFO - All components initialized successfully
2025-06-19 11:53:13,583 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:53:13,583 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:53:13,583 - INFO -   Posts to fetch: 10
2025-06-19 11:53:13,583 - INFO -   Videos to generate: 2
2025-06-19 11:53:13,584 - INFO -   TTS Voice: alloy
2025-06-19 11:53:13,584 - INFO -   Resume mode: False
2025-06-19 11:53:13,584 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:53:13,584 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:53:14,267 - INFO - Successfully fetched 9 posts
2025-06-19 11:53:14,268 - INFO - Filtering post: AITA for using my college fund for a downpayment on a house
2025-06-19 11:53:14,269 - INFO - Filtering post: AITA for calling out my sister in law for using her children to emotionally manipulate me into going to dinner “now” instead of waiting 45 minutes?
2025-06-19 11:53:14,269 - INFO - Filtering post: AITA for not telling her why her dad doesn’t plan to make her his successor?
2025-06-19 11:53:14,269 - INFO - Filtering post: AITA for not hiring my cousin at my startup?
2025-06-19 11:53:14,269 - INFO - Filtering post: AITA – Accidentally outed my dad to his boss
2025-06-19 11:53:14,270 - INFO - Filtering post: AITA, Boyfriend thinks that I have been slacking on household chores due to school?
2025-06-19 11:53:14,270 - INFO - Filtering post: AITAH for telling my boyfriend I dont want to move in with him?
2025-06-19 11:53:14,270 - INFO - Filtering post: AITA for taking back the car I let my sister use after my own car was totaled?
2025-06-19 11:53:14,271 - INFO - Filtering post: WIBTA if I refused to allow my cousin to meet my baby?
2025-06-19 11:53:14,271 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:53:14,271 - ERROR - No valid posts found after filtering
2025-06-19 11:53:38,623 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:53:38,631 - INFO - Reddit API client initialized successfully
2025-06-19 11:53:38,657 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:53:38,665 - INFO - OpenAI client initialized successfully
2025-06-19 11:53:38,672 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:53:38,672 - INFO - All components initialized successfully
2025-06-19 11:53:38,673 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:53:38,673 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:53:38,673 - INFO -   Posts to fetch: 10
2025-06-19 11:53:38,673 - INFO -   Videos to generate: 2
2025-06-19 11:53:38,673 - INFO -   TTS Voice: alloy
2025-06-19 11:53:38,673 - INFO -   Resume mode: False
2025-06-19 11:53:38,673 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:53:38,673 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:53:39,359 - INFO - Successfully fetched 9 posts
2025-06-19 11:53:39,359 - INFO - Filtering post: AITA for using my college fund for a downpayment on a house
2025-06-19 11:53:39,360 - INFO - Cleaned content: Some background- My parents made a college fund fo...
2025-06-19 11:53:39,361 - INFO - Filtering post: AITA for calling out my sister in law for using her children to emotionally manipulate me into going to dinner “now” instead of waiting 45 minutes?
2025-06-19 11:53:39,361 - INFO - Cleaned content: This happened 2 weeks ago. I am 32 and my spouse i...
2025-06-19 11:53:39,361 - INFO - Filtering post: AITA for not telling her why her dad doesn’t plan to make her his successor?
2025-06-19 11:53:39,361 - INFO - Cleaned content: My(20) dad’s best friend ‘Uncle B’ exports bananas...
2025-06-19 11:53:39,362 - INFO - Filtering post: AITA for not hiring my cousin at my startup?
2025-06-19 11:53:39,362 - INFO - Cleaned content: I have a tech startup and one of my aunts reached ...
2025-06-19 11:53:39,362 - INFO - Filtering post: AITA – Accidentally outed my dad to his boss
2025-06-19 11:53:39,362 - INFO - Cleaned content: Euww okay this is my first time posting here/on re...
2025-06-19 11:53:39,362 - INFO - Filtering post: AITA, Boyfriend thinks that I have been slacking on household chores due to school?
2025-06-19 11:53:39,363 - INFO - Cleaned content: I got accepted into nursing school starting this A...
2025-06-19 11:53:39,363 - INFO - Filtering post: AITAH for telling my boyfriend I dont want to move in with him?
2025-06-19 11:53:39,363 - INFO - Cleaned content: I (17f) will be 18 very soon. I will be moving out...
2025-06-19 11:53:39,363 - INFO - Filtering post: AITA for taking back the car I let my sister use after my own car was totaled?
2025-06-19 11:53:39,363 - INFO - Cleaned content: I financed a Mini Cooper under my name almost 3 ye...
2025-06-19 11:53:39,363 - INFO - Filtering post: WIBTA if I refused to allow my cousin to meet my baby?
2025-06-19 11:53:39,364 - INFO - Cleaned content: I (31, F) am giving birth to my first, and likely ...
2025-06-19 11:53:39,364 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:53:39,364 - ERROR - No valid posts found after filtering
2025-06-19 11:53:54,551 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:53:54,558 - INFO - Reddit API client initialized successfully
2025-06-19 11:53:54,584 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:53:54,591 - INFO - OpenAI client initialized successfully
2025-06-19 11:53:54,600 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:53:54,600 - INFO - All components initialized successfully
2025-06-19 11:53:54,601 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:53:54,601 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:53:54,601 - INFO -   Posts to fetch: 10
2025-06-19 11:53:54,601 - INFO -   Videos to generate: 2
2025-06-19 11:53:54,601 - INFO -   TTS Voice: alloy
2025-06-19 11:53:54,601 - INFO -   Resume mode: False
2025-06-19 11:53:54,601 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:53:54,601 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:53:55,327 - INFO - Successfully fetched 9 posts
2025-06-19 11:53:55,328 - INFO - Filtering post: AITA for using my college fund for a downpayment on a house
2025-06-19 11:53:55,329 - INFO - Word count: 307
2025-06-19 11:53:55,329 - INFO - Length penalty: 1.00
2025-06-19 11:53:55,329 - INFO - Filtering post: AITA for calling out my sister in law for using her children to emotionally manipulate me into going to dinner “now” instead of waiting 45 minutes?
2025-06-19 11:53:55,329 - INFO - Word count: 399
2025-06-19 11:53:55,329 - INFO - Length penalty: 1.00
2025-06-19 11:53:55,330 - INFO - Filtering post: AITA for not telling her why her dad doesn’t plan to make her his successor?
2025-06-19 11:53:55,330 - INFO - Word count: 232
2025-06-19 11:53:55,330 - INFO - Length penalty: 1.00
2025-06-19 11:53:55,330 - INFO - Filtering post: AITA for not hiring my cousin at my startup?
2025-06-19 11:53:55,330 - INFO - Word count: 285
2025-06-19 11:53:55,330 - INFO - Length penalty: 1.00
2025-06-19 11:53:55,330 - INFO - Filtering post: AITA – Accidentally outed my dad to his boss
2025-06-19 11:53:55,331 - INFO - Word count: 707
2025-06-19 11:53:55,331 - INFO - Length penalty: 0.30
2025-06-19 11:53:55,331 - INFO - Filtering post: AITA, Boyfriend thinks that I have been slacking on household chores due to school?
2025-06-19 11:53:55,331 - INFO - Word count: 435
2025-06-19 11:53:55,331 - INFO - Length penalty: 1.00
2025-06-19 11:53:55,331 - INFO - Filtering post: AITAH for telling my boyfriend I dont want to move in with him?
2025-06-19 11:53:55,332 - INFO - Word count: 285
2025-06-19 11:53:55,332 - INFO - Length penalty: 1.00
2025-06-19 11:53:55,332 - INFO - Filtering post: AITA for taking back the car I let my sister use after my own car was totaled?
2025-06-19 11:53:55,332 - INFO - Word count: 345
2025-06-19 11:53:55,332 - INFO - Length penalty: 1.00
2025-06-19 11:53:55,332 - INFO - Filtering post: WIBTA if I refused to allow my cousin to meet my baby?
2025-06-19 11:53:55,332 - INFO - Word count: 586
2025-06-19 11:53:55,332 - INFO - Length penalty: 0.52
2025-06-19 11:53:55,332 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:53:55,333 - ERROR - No valid posts found after filtering
2025-06-19 11:54:22,237 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:54:22,244 - INFO - Reddit API client initialized successfully
2025-06-19 11:54:22,271 - INFO - OpenAI reformatter client initialized successfully
2025-06-19 11:54:22,279 - INFO - OpenAI client initialized successfully
2025-06-19 11:54:22,286 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:54:22,287 - INFO - All components initialized successfully
2025-06-19 11:54:22,287 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:54:22,287 - INFO -   Subreddit: r/amitheasshole
2025-06-19 11:54:22,287 - INFO -   Posts to fetch: 10
2025-06-19 11:54:22,287 - INFO -   Videos to generate: 2
2025-06-19 11:54:22,287 - INFO -   TTS Voice: alloy
2025-06-19 11:54:22,287 - INFO -   Resume mode: False
2025-06-19 11:54:22,287 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:54:22,287 - INFO - Fetching top 10 posts from r/amitheasshole
2025-06-19 11:54:22,933 - INFO - Successfully fetched 9 posts
2025-06-19 11:54:22,934 - INFO - Filtering post: AITA for using my college fund for a downpayment on a house
2025-06-19 11:54:22,935 - INFO - Word count: 307
2025-06-19 11:54:22,935 - INFO - Length penalty: 1.00
2025-06-19 11:54:22,935 - INFO - Filter result for post 1lfako2: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language'], 'word_count': 307, 'length_penalty': 1.0, 'cleaned_content': 'Some background- My parents made a college fund for both me and my brother and we both knew about this since we were 13. I used to contribute any extra money I had to my college fund so much so that by the time I turned 18 more than a third of the money in my fund was mine. My brother had his own fund though he did not contribute much into it.\n\nI recently got into college with a full scholarship so I dont really need the money except for some living expenses and yeah to have fun. My brother without informing anyone gambled a good chunk of his college fund on sports betting and lost most of it, we came to know about it very recently when the check bounced.\n\nNow our parents absolutely berated him and denied him any more money but he wont be able to pay for the college even with the money left so now my parents asked me to help him out since I got a scholarship and wont be needing the money anyways so they said I might as well use it to help him. I refused since I had already planned to use that money for downpayment on a house, they then pleaded me to at least give a portion of it otherwise he will have to take on student loans or in a worst case scenario can even miss out on college.\n\nSince then they have been nc with me and my brother has applied for several loans but still has to receive a reply back. Let me add here that he is usually very responsible with his money and this was a rare case\n\nSo AITA for costing my brother a chance at college?'}
2025-06-19 11:54:22,935 - INFO - Skipping invalid post 1lfako2: ['Contains extreme profanity or offensive language']
2025-06-19 11:54:22,935 - INFO - Filtering post: AITA for calling out my sister in law for using her children to emotionally manipulate me into going to dinner “now” instead of waiting 45 minutes?
2025-06-19 11:54:22,936 - INFO - Word count: 399
2025-06-19 11:54:22,936 - INFO - Length penalty: 1.00
2025-06-19 11:54:22,936 - INFO - Filter result for post 1lfhxlp: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language'], 'word_count': 399, 'length_penalty': 1.0, 'cleaned_content': 'This happened 2 weeks ago. I am 32 and my spouse is 29. My spouse and their family (parents, sister, her kids) were hanging out. I was doing some chalk art with the kids while my spouse caught up with everyone. We had a very light lunch and some snacks for everyone to graze while hanging out.\n\nThe time for dinner started to approach and my sister in law brought up heading down for dinner. I said that it was only 5:15 and we should wait until 6:00. She said she would like to go now. I said we should just wait and go at 6 because I had a coupon and there would be deals to make the meal much cheaper. The difference being from estimated $175 to under $75. We had already offered to pay for dinner before they arrived.\n\nMy sister in law grumbled about it and went to check on the kids and their chalk stuff. I didn’t think 45 minutes would be such a big deal. But a few minutes later the kids, who were fine a few minutes ago, came to the patio and were dramatically clutching their stomachs and saying “I’m soooooo hungry” \n\nThis annoyed me. I don’t like it when people use their kids to emotionally manipulate others. I think it’s pathetic. My MIL and FIL were easily swayed and said “Oh, well, let’s just get ready and head down anyway, it’ll be fine.”\n\nI looked at my spouse and they said we could just wait, but their sister said no, and started to get the kids ready to go out. I was super annoyed at this and while the kids ran inside, I turned to her and said “Using your children to emotionally manipulate people into getting your way is pathetic.”\n\nShe said “everyone is hungry, just get over it, if you couldn’t afford to pay for dinner then you shouldn’t have offered.” I do get she has a point that we offered, but is asking for just 45 minutes seriously that big of a deal to have her try to use her kids like that? I would think most family would want to help each other save a little money. Am i wrong?'}
2025-06-19 11:54:22,936 - INFO - Skipping invalid post 1lfhxlp: ['Contains extreme profanity or offensive language']
2025-06-19 11:54:22,936 - INFO - Filtering post: AITA for not telling her why her dad doesn’t plan to make her his successor?
2025-06-19 11:54:22,936 - INFO - Word count: 232
2025-06-19 11:54:22,936 - INFO - Length penalty: 1.00
2025-06-19 11:54:22,936 - INFO - Filter result for post 1lf7m73: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language'], 'word_count': 232, 'length_penalty': 1.0, 'cleaned_content': 'My(20) dad’s best friend ‘Uncle B’ exports bananas. He started paying me to tutor his son ‘C’(14) in English a few months ago, saying that he wants C to take over the banana plantation one day. This came as a surprise since the man also has a daughter ‘D’(13) who is just as smart as and only one year younger than C, and much more interested in the business. C himself said he can’t imagine spending countless hours reading up on growing and harvesting bananas like D does, and that he’d rather become a doctor. He isn’t opposed to the English lessons though, knowing that one day he’ll have to do well on an English proficiency test in order to get into a medical school.\n\nUncle B awkwardly said ‘Well, D’s a girl, so…’\n\nD later complained about how her dad refuses to consider letting her take over the business. I kept quiet, not wanting to stir up trouble/cause an argument between her and Uncle B.\n\nShe eventually confronted him and he told her the reason. When she vented about this, D saw that I wasn’t surprised and asked if I knew. I nodded.\n\nShe said it would’ve been good to know, and that she wouldn’t have felt as hurt had she been ready for the blow.'}
2025-06-19 11:54:22,937 - INFO - Skipping invalid post 1lf7m73: ['Contains extreme profanity or offensive language']
2025-06-19 11:54:22,937 - INFO - Filtering post: AITA for not hiring my cousin at my startup?
2025-06-19 11:54:22,937 - INFO - Word count: 285
2025-06-19 11:54:22,937 - INFO - Length penalty: 1.00
2025-06-19 11:54:22,937 - INFO - Filter result for post 1lfga61: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language'], 'word_count': 285, 'length_penalty': 1.0, 'cleaned_content': "I have a tech startup and one of my aunts reached out because her son is looking for a job and has asked me to hire him.\n\nWe are not close at all but since they are still family, I responded and asked her to have him message me directly so we can have a chat and have him scheduled for an interview. I did not say this exactly, though, and just responded with 'Please have him message me the soonest.'\n\nA week has passed and he has not reached out.\n\nMy aunt messages me again and says I should ping her son and tell him I am offering him a job. This is where I got a bit peeved because 1.) They are asking me for a favour, why should I be the one to reach out? And 2.) I never said I am offering him a job.\n\nI said I am not offering him a job, but an interview, and she completely lost it. She said her son has been jobless for a year and in this economy, I should help them out. 'That is what family is for!', she says. She also then called me names and told me I am 'such a bull's shit' because I promised her a job for her son and he has stopped applying since then.\n\nShe's been flooding me with horrible messages since and at this point I have no plans on giving my cousin a chance at all.\n\nAITA? Was my response really easy to misunderstand?\n\nEDIT: I'm not sure if Reddit has a bug but the comments aren't loading! I appreciate everyone's feedback and advice!"}
2025-06-19 11:54:22,937 - INFO - Skipping invalid post 1lfga61: ['Contains extreme profanity or offensive language']
2025-06-19 11:54:22,937 - INFO - Filtering post: AITA – Accidentally outed my dad to his boss
2025-06-19 11:54:22,938 - INFO - Word count: 707
2025-06-19 11:54:22,938 - INFO - Length penalty: 0.30
2025-06-19 11:54:22,938 - INFO - Filter result for post 1lfezog: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language', 'Post too long (707 words, penalty: 0.30)'], 'word_count': 707, 'length_penalty': 0.3, 'cleaned_content': 'Euww okay this is my first time posting here/on reddit in general so please let me know if I\'m getting anything wrong but here i go\n\nI, 19 F, seemed to have majorly pissed off my dad (49M) and my brain is torn. \n\nBasically, my dad is does work as a caretaker for a few family friends of ours. From what I know, though, he has some sort of deal with them where he\'s not always there and they get to keep some of the money? Idk, all I know is that he just has to get them breakfast, lunch and tea.\n\nWith that being said, he went on holiday earlier this month and, for some reason, didn\'t inform the company?\n\nThey then called me asking for him. Turns out he gave them my number as an emergency contact for some reason, but I didn\'t know this so I didn\'t recognise the number. \n\nWhen they asked, I said he was out of the country, believing it was a doctor or something. Mind you, he NEVER informed me of the possibility that 1. This company had my number 2. That they\'d call or 3. What to say if they did??\n\nNow, he\'s very angry at me in his text messages saying that "I have no right to tell people where he is" and that now he\'s in trouble because of my "big mouth". And other really mean stuff\n\nThey also called the family friend to check up (she didn\'t know she was supposed to cover for him now either) so now they\'re asking to see him ASAP but he doesn\'t come back until Wednesday. And he\'s pissed\n\nThing is, this is a side gig he was planning to quit anyways since it "taxes too much". I\'d understand his anger if I got him in trouble with his main job but I didn\'t? Now he\'s just either insulting me or ignoring me.\n\nI just don\'t know how I was supposed to know?? And my dad has really explosive anger which I hate, he\'s not even here and I feel sick to my stomach anticipating him coming back. He\'s quick to anger in general but recently it feels like everything and anything sets him off, and now I cant discern when I have or haven\'t genuinely messed up. \n\nSo, AITA for telling my dad\'s side job that he was out of the country?\n\nETA : The people calling didn\'t introduce themselves and I didn\'t have their contact saved however when they mentioned the name of the family friend after I said he was out of the country I did try and rectify it by saying I thought they meant someone else and that he\'s here and I\'ll call them. So like imagine :\n\nMe : Hello\n\nThem : Hii, we were just wondering where dads name is? We cant seem to reach him\n\nMe : Oh? Well, my dad is out of the country right now, so that\'s probably why\n\nThem : Oh? But then whos looking after family friend?\n\nMe : long pause Wait did you say my dad? name?\n\nThem : Yes, name.\n\nMe : OHHH uh yeah he\'s here, I\'ll ask him to call you quickly hangs up\n\nObviously not the best but I did try too but he doesn\'t believe me\n\nDoubley edit : I don\'t know if my reddit is glitching or if the comments are getting deleted but some will appear in my notifs but I won\'t be able to open them so sorry about that :( \n\nAnyhow, for people asking why I answered and told them that, I\'ll be honest, I guess I\'m just really naive/stupid haha. I\'ve never really had to answer on behalf on anyone but myself before so I guess it caught me off guard. To be fair, there\'s an area code for numbers where I live thats typically used for official institutions and because both me and my dad are diabetic, I had assumed it was either one of our GPs or clinics. I honestly didnt think revealing that my dad wasnt in the country was super dangerous, but I know better now, so thank you all for the advice!!'}
2025-06-19 11:54:22,938 - INFO - Skipping invalid post 1lfezog: ['Contains extreme profanity or offensive language', 'Post too long (707 words, penalty: 0.30)']
2025-06-19 11:54:22,938 - INFO - Filtering post: AITA, Boyfriend thinks that I have been slacking on household chores due to school?
2025-06-19 11:54:22,938 - INFO - Word count: 435
2025-06-19 11:54:22,938 - INFO - Length penalty: 1.00
2025-06-19 11:54:22,939 - INFO - Filter result for post 1lfcc9g: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language'], 'word_count': 435, 'length_penalty': 1.0, 'cleaned_content': 'I got accepted into nursing school starting this August. My admission is dependent upon me completing CHM 151. I failed the last class that I took because it was online, no lecture, and we were given a textbook and links to YouTube videos to learn. I enrolled in another 5 week CHM 151 class in person, so you can imagine how much material we cover and how fast paced it is. \n\nSchool is extremely important to me, as my priority is to be a nurse. School is very challenging to me, and I often get lost and have trouble prioritizing other things in my life such as my boyfriend, the dogs, and our house. I will spend hours sitting at my desk studying. It doesn’t help that I have my 3rd exam Monday and final exam on Thursday of next week. \n\nYesterday, my boyfriend came home and was upset for several reasons. 1, I didn’t put a diaper on the dog before he got home because I forgot. (Dog pees when he sees him). 2. The house was “disgusting”, and I didn’t sweep. 3. My phone was dying and he “couldn’t communicate with me”. 4. I didn’t put the garbage/recycling bins back. After he got home and the dog peed, he started yelling, threw a diaper across the room, and asked me to put it on the dog because the dog didn’t want to go near him. Then he started accusing me of doing nothing all day when I went to class and studied for the whole day. \n\nWe went to bed, and the next morning he texts me this. “I felt I needed to literally make you a chores list because you won’t do anything unless I ask you. Think of this list as a reminder, to be present, to not neglect this house, me, the boys, even when you are studying, god help us all when you’re in nursing school, schedule your time to get it done, or I will pay someone to do it and their payment is coming out of your payroll. This is only a partial list, there’s more, I just didn’t want to overwhelm you.” When he says payroll, he means that he essentially covers all my bills while I’m going through schooling until I become a nurse. \n\nThis worries me because I don’t feel like he’ll be understanding when I start nursing school, however I understand that he wants a partner that pulls their weight. I just feel like he’s being extremely aggressive. AITA?'}
2025-06-19 11:54:22,939 - INFO - Skipping invalid post 1lfcc9g: ['Contains extreme profanity or offensive language']
2025-06-19 11:54:22,939 - INFO - Filtering post: AITAH for telling my boyfriend I dont want to move in with him?
2025-06-19 11:54:22,939 - INFO - Word count: 285
2025-06-19 11:54:22,939 - INFO - Length penalty: 1.00
2025-06-19 11:54:22,939 - INFO - Filter result for post 1lfgl8t: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language'], 'word_count': 285, 'length_penalty': 1.0, 'cleaned_content': 'I (17f) will be 18 very soon. I will be moving out the day I turn 18. My boyfriend (18m) wants me to to move in with him. Now this wouldnt be an issue but he is still living with his mom. If his mom would let me bring my dog then I would but I am unable to bring him. He is a service dog so ge goes everywhere with me. Now my boyfriend is mad at me because I will not leave my service dog behind to live with him. In the text he states "You are being selfish. It\'s just a dog"\n\nTo me my dog is not just a dog. He is family to me. I will not leave him with my mother. \n\nMe and my boyfriend have been arguing back and forth for days. He says he cant be with someone who won\'t live with him. I understand that he is 18 and getting on his feet so he doesn\'t have his own place. But, I at 17 have a place set with my best friend, her cat and her dog. He is mad about that aswell. \n\nAs well as if me and him broke up while I was living there I would have to move out the same day. This has already been a rule set by his mother because it is her house. So I would have to walk on eggshells around everyone if I lived with my boyfriend. \n\nIf he had his own place this would be different but at this moment I dont want to live there. Does this make me an asshole?'}
2025-06-19 11:54:22,939 - INFO - Skipping invalid post 1lfgl8t: ['Contains extreme profanity or offensive language']
2025-06-19 11:54:22,939 - INFO - Filtering post: AITA for taking back the car I let my sister use after my own car was totaled?
2025-06-19 11:54:22,940 - INFO - Word count: 345
2025-06-19 11:54:22,940 - INFO - Length penalty: 1.00
2025-06-19 11:54:22,940 - INFO - Filter result for post 1lfdyhg: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language'], 'word_count': 345, 'length_penalty': 1.0, 'cleaned_content': 'I financed a Mini Cooper under my name almost 3 years ago. I paid for the first full year (about 8.5k), then let my younger sister use it when she needed a car for college. My parents took over the payments after that (about 1.7 years), but the loan has always been under my name. I also paid for new wheels before giving her the car (they said it’s like “buying a new car” so the wheels should be new…) \n\nAt the time, I financed a Tesla for myself under my parents’ name. They claimed the $7,500 EV tax credit and promised to pay me back, but never did. I was gonna use this to pay off the Tesla faster. \n\nNow the Tesla is totaled and I’m carless. My credit has dropped and it wouldn’t be smart to finance or lease anything right now. Meanwhile, my sister is still driving the Mini, which I’m still legally responsible for.\n\nI told my family I need to take the Mini back and finish the payments. I’m not asking to be paid back for the first year, the wheels, or the tax credit. I just need the car I’m still tied to.\n\nI feel like a lil bitch but also it doesn’t feel fair that my sister got a newer car in college and post grad while I had to drive a very very old car that broke down so much 😭 until I could get a new car. \n\nAm I the asshole? \n\nEDIT: Debris collision on the road destroyed by battery which totaled the car (unavoidable debris it was either hard stop and get rear ended or move to the next lane over and hit a huge vehicle. There was no shoulder). \n\nThere is also about 2 more years on the car loan, which I would continue to pay for. My sister has graduated from college and would like to pay off her loans before paying for a car herself.'}
2025-06-19 11:54:22,940 - INFO - Skipping invalid post 1lfdyhg: ['Contains extreme profanity or offensive language']
2025-06-19 11:54:22,940 - INFO - Filtering post: WIBTA if I refused to allow my cousin to meet my baby?
2025-06-19 11:54:22,940 - INFO - Word count: 586
2025-06-19 11:54:22,940 - INFO - Length penalty: 0.52
2025-06-19 11:54:22,940 - INFO - Filter result for post 1lf8rcg: {'is_valid': False, 'reasons': ['Contains extreme profanity or offensive language', 'Post too long (586 words, penalty: 0.52)'], 'word_count': 586, 'length_penalty': 0.5204072757786532, 'cleaned_content': 'I (31, F) am giving birth to my first, and likely only, child within the next two weeks. I\'m 38 weeks this weekend, and my husband (29, M) and I are very excited! However, we\'re facing a few issues that come with newborns and family members who don\'t know how to respect boundaries, and one in particular is REALLY bothering me.\n\nAlong with the usual, regarding no kisses, no posting pictures, and no taking the baby from me or her father, we are enforcing a strict rule about visits. During the first week, the only visitors allowed will be my mom and my sister, who will be helping me and my husband with chores and cooking while I am down for recovery. The second and third week, my husband\'s mother will be coming to stay, and the three of them will be the only ones allowed around the baby. All other visitors must wait until week four, after she\'s had time to acclimate her immune system a little bit, get her feeding situation figured out (whether or not I can produce milk or if she will need formula), and get me healthy enough that I can get around and take care of the baby and the house without extensive assistance. (The pregnancy has been hard for me health-wise, and the doctors have predicted a difficult recovery.) We also have the rule that anyone who has not been an active part of OUR lives pre-baby, has not checked in on us during pregnancy, or is not willing to abide by our boundaries will NOT be meeting the baby as a newborn, if ever. There\'s literally no need for her to be exposed to a bunch of people that don\'t matter in her life as a newborn.\n\nToday, my mom told me that her cousin (so my second cousin??) wants to come in from out of state and meet the baby while I\'m in my maternity leave period. I do not like this cousin, at all. She\'s a very aggressive Bible-thumper, who holds nasty beliefs about gender, race, and religion, and she spouts her crap off on anyone she can get her hands on. I was passively fine with her until a couple of years ago, when my mom had a heart attack. This cousin told me, an absolute wreck of a human who was considering leaving this world myself if my mother didn\'t make it, that God gave my mom the heart attack to teach her a lesson, and that if she survived, it would only be because God decided she deserved a second chance. I have hated her with a burning passion ever since.\n\nAnyways, when my mom told me that the cousin was going to come visit, I said "No." My mom yelled at me to just get over it (she knows EXACTLY why I hate this cousin) and I need to let her come meet the baby, because she\'s like a sister to my mom (they grew up very closely together), and its important that the matriarchs of the family (her, Bible-thumper and her mom, and myself are the only women in the family) meet the new baby girl. I didn\'t have time to argue with her, because I had to get to work, so I dropped it, but it has bothered me all day.\n\nWIBTA if I refused to let my cousin meet my newborn just because I don\'t like her?'}
2025-06-19 11:54:22,940 - INFO - Skipping invalid post 1lf8rcg: ['Contains extreme profanity or offensive language', 'Post too long (586 words, penalty: 0.52)']
2025-06-19 11:54:22,940 - INFO - Filtered 9 posts down to 0 valid posts
2025-06-19 11:54:22,941 - ERROR - No valid posts found after filtering
