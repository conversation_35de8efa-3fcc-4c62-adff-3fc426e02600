2025-06-18 23:52:56,835 - INFO - Initializing Reddit2Shorts components...
2025-06-18 23:52:56,842 - INFO - Reddit API client initialized successfully
2025-06-18 23:52:56,901 - INFO - OpenAI client initialized successfully
2025-06-18 23:52:56,914 - INFO - OpenAI TTS client initialized successfully
2025-06-18 23:52:56,914 - INFO - All components initialized successfully
2025-06-18 23:52:56,915 - INFO - Starting Reddit2Shorts pipeline:
2025-06-18 23:52:56,915 - INFO -   Subreddit: r/amitheasshole
2025-06-18 23:52:56,915 - INFO -   Posts to fetch: 20
2025-06-18 23:52:56,915 - INFO -   Videos to generate: 1
2025-06-18 23:52:56,915 - INFO -   TTS Voice: onyx
2025-06-18 23:52:56,915 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-18 23:52:56,916 - INFO - Fetching top 20 posts from r/amitheasshole
2025-06-18 23:52:57,754 - INFO - Successfully fetched 19 posts
2025-06-18 23:53:17,368 - INFO - Filtered 19 posts down to 12 valid posts
2025-06-18 23:53:17,368 - INFO - Found 12 valid posts after filtering
2025-06-18 23:53:17,368 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-18 23:53:17,368 - INFO - Grading post 1/12: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:53:22,096 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:22,110 - INFO - Grading post 2/12: AITA for moving into a studio apartment away from ...
2025-06-18 23:53:26,535 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:26,541 - INFO - Grading post 3/12: AITA for telling my dad I felt excluded after his ...
2025-06-18 23:53:30,902 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:30,904 - INFO - Grading post 4/12: AITA for telling my mom that I don't care if she l...
2025-06-18 23:53:36,330 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:36,331 - INFO - Grading post 5/12: AITA for having lunch in a cemetery?...
2025-06-18 23:53:41,603 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:41,606 - INFO - Grading post 6/12: AITA for cutting my honeymoon short to be here for...
2025-06-18 23:53:46,572 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:46,581 - INFO - Grading post 7/12: AITA for turning down a weird solicitor in a way m...
2025-06-18 23:53:51,074 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:51,076 - INFO - Grading post 8/12: AITA for standing my ground and keeping my baby ev...
2025-06-18 23:53:55,274 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:55,276 - INFO - Grading post 9/12: AITA for asking my housemate to pay more for utili...
2025-06-18 23:53:59,543 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:59,546 - INFO - Grading post 10/12: WIBTA if I paid my SIL what I think she should get...
2025-06-18 23:54:04,051 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:04,053 - INFO - Grading post 11/12: AITA for making my husbands friends cancel a holid...
2025-06-18 23:54:08,079 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:08,082 - INFO - Grading post 12/12: AITA for not cleaning after I finished house/pet s...
2025-06-18 23:54:11,665 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:11,668 - INFO - Successfully graded 12/12 posts
2025-06-18 23:54:11,668 - INFO - Selected top 1 posts for video generation:
2025-06-18 23:54:11,668 - INFO -   1. AITA for asking my boyfriend to contribute to groc... (score: 5.20)
2025-06-18 23:54:11,669 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-18 23:54:11,669 - INFO - Generating audio 1/1
2025-06-18 23:54:11,669 - INFO - Generating TTS audio for post: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:54:11,669 - INFO - Using voice: onyx
2025-06-18 23:54:11,669 - INFO - Text length: 1218 characters
2025-06-18 23:54:13,615 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-18 23:54:24,706 - INFO - Audio generated successfully: temp_audio/aitagroceriesboyfriend_audio.mp3
2025-06-18 23:54:24,706 - INFO - Generated audio for 1/1 posts
2025-06-18 23:54:24,706 - INFO - 
=== Step 4: Creating videos ===
2025-06-18 23:54:24,706 - INFO - Creating video 1/1: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:54:24,706 - ERROR - Error getting audio duration for temp_audio/aitagroceriesboyfriend_audio.mp3: module 'ffmpeg' has no attribute 'probe'
2025-06-18 23:54:24,706 - ERROR - Could not determine audio duration
2025-06-18 23:54:24,706 - WARNING - Failed to create video for post 1lezl8q
2025-06-18 23:54:24,706 - INFO - Created 0/1 videos successfully
2025-06-18 23:54:24,706 - ERROR - No videos created
