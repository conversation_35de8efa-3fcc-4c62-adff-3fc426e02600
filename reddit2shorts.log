2025-06-18 23:52:56,835 - INFO - Initializing Reddit2Shorts components...
2025-06-18 23:52:56,842 - INFO - Reddit API client initialized successfully
2025-06-18 23:52:56,901 - INFO - OpenAI client initialized successfully
2025-06-18 23:52:56,914 - INFO - OpenAI TTS client initialized successfully
2025-06-18 23:52:56,914 - INFO - All components initialized successfully
2025-06-18 23:52:56,915 - INFO - Starting Reddit2Shorts pipeline:
2025-06-18 23:52:56,915 - INFO -   Subreddit: r/amitheasshole
2025-06-18 23:52:56,915 - INFO -   Posts to fetch: 20
2025-06-18 23:52:56,915 - INFO -   Videos to generate: 1
2025-06-18 23:52:56,915 - INFO -   TTS Voice: onyx
2025-06-18 23:52:56,915 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-18 23:52:56,916 - INFO - Fetching top 20 posts from r/amitheasshole
2025-06-18 23:52:57,754 - INFO - Successfully fetched 19 posts
2025-06-18 23:53:17,368 - INFO - Filtered 19 posts down to 12 valid posts
2025-06-18 23:53:17,368 - INFO - Found 12 valid posts after filtering
2025-06-18 23:53:17,368 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-18 23:53:17,368 - INFO - Grading post 1/12: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:53:22,096 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:22,110 - INFO - Grading post 2/12: AITA for moving into a studio apartment away from ...
2025-06-18 23:53:26,535 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:26,541 - INFO - Grading post 3/12: AITA for telling my dad I felt excluded after his ...
2025-06-18 23:53:30,902 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:30,904 - INFO - Grading post 4/12: AITA for telling my mom that I don't care if she l...
2025-06-18 23:53:36,330 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:36,331 - INFO - Grading post 5/12: AITA for having lunch in a cemetery?...
2025-06-18 23:53:41,603 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:41,606 - INFO - Grading post 6/12: AITA for cutting my honeymoon short to be here for...
2025-06-18 23:53:46,572 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:46,581 - INFO - Grading post 7/12: AITA for turning down a weird solicitor in a way m...
2025-06-18 23:53:51,074 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:51,076 - INFO - Grading post 8/12: AITA for standing my ground and keeping my baby ev...
2025-06-18 23:53:55,274 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:55,276 - INFO - Grading post 9/12: AITA for asking my housemate to pay more for utili...
2025-06-18 23:53:59,543 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:53:59,546 - INFO - Grading post 10/12: WIBTA if I paid my SIL what I think she should get...
2025-06-18 23:54:04,051 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:04,053 - INFO - Grading post 11/12: AITA for making my husbands friends cancel a holid...
2025-06-18 23:54:08,079 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:08,082 - INFO - Grading post 12/12: AITA for not cleaning after I finished house/pet s...
2025-06-18 23:54:11,665 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-18 23:54:11,668 - INFO - Successfully graded 12/12 posts
2025-06-18 23:54:11,668 - INFO - Selected top 1 posts for video generation:
2025-06-18 23:54:11,668 - INFO -   1. AITA for asking my boyfriend to contribute to groc... (score: 5.20)
2025-06-18 23:54:11,669 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-18 23:54:11,669 - INFO - Generating audio 1/1
2025-06-18 23:54:11,669 - INFO - Generating TTS audio for post: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:54:11,669 - INFO - Using voice: onyx
2025-06-18 23:54:11,669 - INFO - Text length: 1218 characters
2025-06-18 23:54:13,615 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-18 23:54:24,706 - INFO - Audio generated successfully: temp_audio/aitagroceriesboyfriend_audio.mp3
2025-06-18 23:54:24,706 - INFO - Generated audio for 1/1 posts
2025-06-18 23:54:24,706 - INFO - 
=== Step 4: Creating videos ===
2025-06-18 23:54:24,706 - INFO - Creating video 1/1: AITA for asking my boyfriend to contribute to groc...
2025-06-18 23:54:24,706 - ERROR - Error getting audio duration for temp_audio/aitagroceriesboyfriend_audio.mp3: module 'ffmpeg' has no attribute 'probe'
2025-06-18 23:54:24,706 - ERROR - Could not determine audio duration
2025-06-18 23:54:24,706 - WARNING - Failed to create video for post 1lezl8q
2025-06-18 23:54:24,706 - INFO - Created 0/1 videos successfully
2025-06-18 23:54:24,706 - ERROR - No videos created
2025-06-19 11:18:27,101 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:18:27,231 - INFO - Reddit API client initialized successfully
2025-06-19 11:18:27,293 - INFO - OpenAI client initialized successfully
2025-06-19 11:18:27,302 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:18:27,302 - INFO - All components initialized successfully
2025-06-19 11:18:27,303 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:18:27,303 - INFO -   Subreddit: r/tifu
2025-06-19 11:18:27,303 - INFO -   Posts to fetch: 50
2025-06-19 11:18:27,303 - INFO -   Videos to generate: 3
2025-06-19 11:18:27,303 - INFO -   TTS Voice: echo
2025-06-19 11:18:27,303 - INFO -   Resume mode: False
2025-06-19 11:18:27,303 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:18:27,303 - INFO - Fetching top 50 posts from r/tifu
2025-06-19 11:18:28,721 - INFO - Successfully fetched 50 posts
2025-06-19 11:18:37,587 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:18:37,594 - INFO - Reddit API client initialized successfully
2025-06-19 11:18:37,620 - INFO - OpenAI client initialized successfully
2025-06-19 11:18:37,626 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:18:37,627 - INFO - All components initialized successfully
2025-06-19 11:18:37,627 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:18:37,627 - INFO -   Subreddit: r/tifu
2025-06-19 11:18:37,627 - INFO -   Posts to fetch: 3
2025-06-19 11:18:37,627 - INFO -   Videos to generate: 1
2025-06-19 11:18:37,627 - INFO -   TTS Voice: echo
2025-06-19 11:18:37,627 - INFO -   Resume mode: False
2025-06-19 11:18:37,628 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:18:37,628 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:18:38,151 - INFO - Successfully fetched 3 posts
2025-06-19 11:18:39,860 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:18:39,861 - INFO - Found 1 valid posts after filtering
2025-06-19 11:18:39,861 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:18:39,861 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:18:44,177 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:18:44,194 - INFO - Successfully graded 1/1 posts
2025-06-19 11:18:44,194 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:18:44,194 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 4.80)
2025-06-19 11:18:44,194 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:18:44,194 - INFO - Generating audio 1/1
2025-06-19 11:18:44,194 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:18:44,194 - INFO - Using voice: echo
2025-06-19 11:18:44,195 - INFO - Text length: 1166 characters
2025-06-19 11:18:46,208 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:18:53,955 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:18:53,955 - INFO - Generated audio for 1/1 posts
2025-06-19 11:18:53,955 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:18:53,955 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:18:53,982 - INFO - Audio duration: 63.58 seconds
2025-06-19 11:18:53,982 - INFO - Found 1 B-roll videos
2025-06-19 11:18:53,982 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:18:53,983 - WARNING - No B-roll videos long enough for 63.58s, using longest available
2025-06-19 11:18:53,983 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:18:53,983 - ERROR - Could not select suitable B-roll video
2025-06-19 11:18:53,983 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:18:53,983 - INFO - Created 0/1 videos successfully
2025-06-19 11:18:53,983 - ERROR - No videos created
2025-06-19 11:20:28,521 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:20:28,528 - INFO - Reddit API client initialized successfully
2025-06-19 11:20:28,555 - INFO - OpenAI client initialized successfully
2025-06-19 11:20:28,562 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:20:28,562 - INFO - All components initialized successfully
2025-06-19 11:20:28,563 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:20:28,563 - INFO -   Subreddit: r/tifu
2025-06-19 11:20:28,563 - INFO -   Posts to fetch: 3
2025-06-19 11:20:28,563 - INFO -   Videos to generate: 1
2025-06-19 11:20:28,563 - INFO -   TTS Voice: echo
2025-06-19 11:20:28,563 - INFO -   Resume mode: False
2025-06-19 11:20:28,563 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:20:28,563 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:20:29,139 - INFO - Successfully fetched 3 posts
2025-06-19 11:20:30,831 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:20:30,831 - INFO - Found 1 valid posts after filtering
2025-06-19 11:20:30,831 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:20:30,831 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:20:35,129 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:20:35,140 - INFO - Successfully graded 1/1 posts
2025-06-19 11:20:35,141 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:20:35,141 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 4.80)
2025-06-19 11:20:35,141 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:20:35,141 - INFO - Generating audio 1/1
2025-06-19 11:20:35,141 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:20:35,141 - INFO - Using voice: echo
2025-06-19 11:20:35,141 - INFO - Text length: 1166 characters
2025-06-19 11:20:37,938 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:20:49,453 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:20:49,453 - INFO - Generated audio for 1/1 posts
2025-06-19 11:20:49,453 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:20:49,454 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:20:49,466 - INFO - Audio duration: 63.00 seconds
2025-06-19 11:20:49,467 - INFO - Found 1 B-roll videos
2025-06-19 11:20:49,467 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:20:49,467 - WARNING - No B-roll videos long enough for 63.00s, using longest available
2025-06-19 11:20:49,467 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:20:49,467 - ERROR - Could not select suitable B-roll video
2025-06-19 11:20:49,467 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:20:49,467 - INFO - Created 0/1 videos successfully
2025-06-19 11:20:49,467 - ERROR - No videos created
2025-06-19 11:22:13,447 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:22:13,454 - INFO - Reddit API client initialized successfully
2025-06-19 11:22:13,480 - INFO - OpenAI client initialized successfully
2025-06-19 11:22:13,488 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:22:13,488 - INFO - All components initialized successfully
2025-06-19 11:22:13,489 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:22:13,489 - INFO -   Subreddit: r/tifu
2025-06-19 11:22:13,489 - INFO -   Posts to fetch: 3
2025-06-19 11:22:13,489 - INFO -   Videos to generate: 1
2025-06-19 11:22:13,489 - INFO -   TTS Voice: echo
2025-06-19 11:22:13,489 - INFO -   Resume mode: False
2025-06-19 11:22:13,489 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:22:13,490 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:22:14,055 - INFO - Successfully fetched 3 posts
2025-06-19 11:22:15,754 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:22:15,754 - INFO - Found 1 valid posts after filtering
2025-06-19 11:22:15,754 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:22:15,754 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:22:20,908 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:22:20,920 - INFO - Successfully graded 1/1 posts
2025-06-19 11:22:20,920 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:22:20,920 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 5.10)
2025-06-19 11:22:20,920 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:22:20,920 - INFO - Generating audio 1/1
2025-06-19 11:22:20,920 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:22:20,921 - INFO - Using voice: echo
2025-06-19 11:22:20,921 - INFO - Text length: 1166 characters
2025-06-19 11:22:22,381 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:22:29,842 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:22:29,843 - INFO - Generated audio for 1/1 posts
2025-06-19 11:22:29,843 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:22:29,843 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:22:29,855 - INFO - Audio duration: 63.12 seconds
2025-06-19 11:22:29,856 - INFO - Found 1 B-roll videos
2025-06-19 11:22:29,856 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:22:29,856 - WARNING - No B-roll videos long enough for 63.12s, using longest available
2025-06-19 11:22:29,856 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:22:29,856 - ERROR - Could not select suitable B-roll video
2025-06-19 11:22:29,856 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:22:29,856 - INFO - Created 0/1 videos successfully
2025-06-19 11:22:29,856 - ERROR - No videos created
2025-06-19 11:23:48,490 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:23:48,500 - INFO - Reddit API client initialized successfully
2025-06-19 11:23:48,535 - INFO - OpenAI client initialized successfully
2025-06-19 11:23:48,545 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:23:48,545 - INFO - All components initialized successfully
2025-06-19 11:23:48,545 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:23:48,546 - INFO -   Subreddit: r/tifu
2025-06-19 11:23:48,546 - INFO -   Posts to fetch: 3
2025-06-19 11:23:48,546 - INFO -   Videos to generate: 1
2025-06-19 11:23:48,546 - INFO -   TTS Voice: echo
2025-06-19 11:23:48,546 - INFO -   Resume mode: False
2025-06-19 11:23:48,546 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:23:48,546 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:23:49,071 - INFO - Successfully fetched 3 posts
2025-06-19 11:23:50,793 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:23:50,793 - INFO - Found 1 valid posts after filtering
2025-06-19 11:23:50,793 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:23:50,793 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:23:55,381 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:23:55,392 - INFO - Successfully graded 1/1 posts
2025-06-19 11:23:55,392 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:23:55,393 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 5.10)
2025-06-19 11:23:55,393 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:23:55,393 - INFO - Generating audio 1/1
2025-06-19 11:23:55,393 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:23:55,393 - INFO - Using voice: echo
2025-06-19 11:23:55,393 - INFO - Text length: 1166 characters
2025-06-19 11:23:57,750 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:24:11,147 - INFO - Audio generated successfully: temp_audio/deafadvicefail_audio.mp3
2025-06-19 11:24:11,147 - INFO - Generated audio for 1/1 posts
2025-06-19 11:24:11,147 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:24:11,147 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:24:11,159 - INFO - Audio duration: 63.48 seconds
2025-06-19 11:24:11,160 - INFO - Found 1 B-roll videos
2025-06-19 11:24:11,160 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:24:11,160 - WARNING - No B-roll videos long enough for 63.48s, using longest available
2025-06-19 11:24:11,160 - ERROR - Error getting video duration for broll/minecraft_parkour.mp4: module 'ffmpeg' has no attribute 'probe'
2025-06-19 11:24:11,160 - ERROR - Could not select suitable B-roll video
2025-06-19 11:24:11,160 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:24:11,160 - INFO - Created 0/1 videos successfully
2025-06-19 11:24:11,160 - ERROR - No videos created
2025-06-19 11:24:33,605 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:24:33,611 - INFO - Reddit API client initialized successfully
2025-06-19 11:24:33,637 - INFO - OpenAI client initialized successfully
2025-06-19 11:24:33,645 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:24:33,645 - INFO - All components initialized successfully
2025-06-19 11:24:33,645 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:24:33,645 - INFO -   Subreddit: r/tifu
2025-06-19 11:24:33,645 - INFO -   Posts to fetch: 3
2025-06-19 11:24:33,645 - INFO -   Videos to generate: 1
2025-06-19 11:24:33,646 - INFO -   TTS Voice: echo
2025-06-19 11:24:33,646 - INFO -   Resume mode: False
2025-06-19 11:24:33,646 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:24:33,646 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:24:34,175 - INFO - Successfully fetched 3 posts
2025-06-19 11:24:35,901 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:24:35,901 - INFO - Found 1 valid posts after filtering
2025-06-19 11:24:35,901 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:24:35,901 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:24:39,918 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:24:39,929 - INFO - Successfully graded 1/1 posts
2025-06-19 11:24:39,930 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:24:39,930 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 5.50)
2025-06-19 11:24:39,930 - INFO - 
=== Step 3: Generating audio narration ===
2025-06-19 11:24:39,930 - INFO - Generating audio 1/1
2025-06-19 11:24:39,930 - INFO - Generating TTS audio for post: TIFU by giving a colleague sound advice....
2025-06-19 11:24:39,930 - INFO - Using voice: echo
2025-06-19 11:24:39,930 - INFO - Text length: 1166 characters
2025-06-19 11:24:42,559 - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-19 11:24:48,476 - INFO - Audio generated successfully: temp_audio/deafaudiobookadvice_audio.mp3
2025-06-19 11:24:48,476 - INFO - Generated audio for 1/1 posts
2025-06-19 11:24:48,476 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:24:48,476 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:24:48,488 - INFO - Audio duration: 63.12 seconds
2025-06-19 11:24:48,489 - INFO - Found 1 B-roll videos
2025-06-19 11:24:51,233 - WARNING - No B-roll videos long enough for 63.12s, using longest available
2025-06-19 11:24:51,480 - INFO - Creating video: deafaudiobookadvice.mp4
2025-06-19 11:24:51,480 - INFO - B-roll: minecraft_parkour.mp4
2025-06-19 11:24:51,701 - ERROR - Error creating video for post 1lez58m: module 'ffmpeg' has no attribute 'input'
2025-06-19 11:24:51,701 - WARNING - Failed to create video for post 1lez58m
2025-06-19 11:24:51,701 - INFO - Created 0/1 videos successfully
2025-06-19 11:24:51,701 - ERROR - No videos created
2025-06-19 11:26:26,289 - INFO - Initializing Reddit2Shorts components...
2025-06-19 11:26:26,297 - INFO - Reddit API client initialized successfully
2025-06-19 11:26:26,336 - INFO - OpenAI client initialized successfully
2025-06-19 11:26:26,345 - INFO - OpenAI TTS client initialized successfully
2025-06-19 11:26:26,345 - INFO - All components initialized successfully
2025-06-19 11:26:26,346 - INFO - Starting Reddit2Shorts pipeline:
2025-06-19 11:26:26,346 - INFO -   Subreddit: r/tifu
2025-06-19 11:26:26,346 - INFO -   Posts to fetch: 3
2025-06-19 11:26:26,346 - INFO -   Videos to generate: 1
2025-06-19 11:26:26,346 - INFO -   TTS Voice: onyx
2025-06-19 11:26:26,346 - INFO -   Resume mode: True
2025-06-19 11:26:26,346 - INFO - 
=== Step 1: Fetching Reddit posts ===
2025-06-19 11:26:26,346 - INFO - Fetching top 3 posts from r/tifu
2025-06-19 11:26:27,067 - INFO - Successfully fetched 3 posts
2025-06-19 11:26:28,762 - INFO - Filtered 3 posts down to 1 valid posts
2025-06-19 11:26:28,763 - INFO - Found 1 valid posts after filtering
2025-06-19 11:26:28,763 - INFO - 
=== Step 2: Grading posts with AI ===
2025-06-19 11:26:28,763 - INFO - Grading post 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:26:32,745 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 11:26:32,756 - INFO - Successfully graded 1/1 posts
2025-06-19 11:26:32,757 - INFO - Selected top 1 posts for video generation:
2025-06-19 11:26:32,757 - INFO -   1. TIFU by giving a colleague sound advice.... (score: 4.80)
2025-06-19 11:26:32,757 - INFO - 
=== Step 3: Checking for existing audio files ===
2025-06-19 11:26:32,757 - INFO - Found 3 existing audio files
2025-06-19 11:26:32,757 - INFO - Existing audio files:
2025-06-19 11:26:32,758 - INFO -   - aitagroceriesboyfriend: aitagroceriesboyfriend_audio.mp3
2025-06-19 11:26:32,758 - INFO -   - deafaudiobookadvice: deafaudiobookadvice_audio.mp3
2025-06-19 11:26:32,758 - INFO -   - deafadvicefail: deafadvicefail_audio.mp3
2025-06-19 11:26:32,758 - INFO - ✅ Found audio for: TIFU by giving a colleague sound advice....
2025-06-19 11:26:32,758 - INFO - 🔄 Reusing 1 existing audio files
2025-06-19 11:26:32,758 - INFO - ✅ Using 1 total audio files (1 reused + 0 generated)
2025-06-19 11:26:32,758 - INFO - 
=== Step 4: Creating videos ===
2025-06-19 11:26:32,758 - INFO - Creating video 1/1: TIFU by giving a colleague sound advice....
2025-06-19 11:26:32,771 - INFO - Audio duration: 63.12 seconds
2025-06-19 11:26:32,772 - INFO - Found 1 B-roll videos
2025-06-19 11:26:33,563 - WARNING - No B-roll videos long enough for 63.12s, using longest available
2025-06-19 11:26:33,800 - INFO - Creating video: deafaudiobookadvice.mp4
2025-06-19 11:26:33,800 - INFO - B-roll: minecraft_parkour.mp4
2025-06-19 11:27:22,843 - INFO - Video created successfully: output_videos/deafaudiobookadvice.mp4
2025-06-19 11:27:22,844 - INFO - Created 1/1 videos successfully
2025-06-19 11:27:22,844 - INFO - 
=== Pipeline completed successfully! ===
2025-06-19 11:27:22,844 - INFO - Generated 1 videos in 56.50 seconds
2025-06-19 11:27:22,845 - INFO - Videos saved to: output_videos
2025-06-19 11:27:22,845 - INFO -   📹 deafaudiobookadvice.mp4 - TIFU by giving a colleague sound advice....
2025-06-19 11:27:22,845 - INFO - 💾 Audio files preserved for future resume operations
