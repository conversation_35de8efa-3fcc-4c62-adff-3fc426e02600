"""
OpenAI LLM-based post grading system for ranking Reddit posts.
"""

import os
import json
import logging
import re
from typing import Dict, List, Optional
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class PostGrader:
    def __init__(self):
        """Initialize OpenAI client for post grading."""
        self.client = None
        self._setup_openai_client()
        self._load_grading_prompt()
    
    def _setup_openai_client(self):
        """Set up OpenAI API client."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        
        self.client = OpenAI(api_key=api_key)
        logging.info("OpenAI client initialized successfully")
    
    def _load_grading_prompt(self):
        """Load the grading prompt template."""
        try:
            with open('prompts/grading_prompt.txt', 'r') as f:
                self.grading_prompt_template = f.read()
        except FileNotFoundError:
            logging.error("Grading prompt template not found")
            raise
    
    def _create_filename_summary(self, title: str, content: str) -> str:
        """Create a 2-5 word lowercase alphanumeric summary for filename."""
        # Combine title and first part of content
        text = f"{title} {content[:100]}"
        
        # Remove non-alphanumeric characters and convert to lowercase
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        
        # Filter out common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'am', 'my', 'me', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'this', 'that', 'these', 'those'}
        
        meaningful_words = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Take first 2-5 words
        summary_words = meaningful_words[:5] if len(meaningful_words) >= 5 else meaningful_words[:max(2, len(meaningful_words))]
        
        # Join with underscores
        summary = '_'.join(summary_words)
        
        # Ensure it's not empty
        if not summary:
            summary = 'reddit_post'
        
        return summary
    
    def grade_post(self, post: Dict) -> Optional[Dict]:
        """
        Grade a single post using OpenAI GPT-4o.
        
        Args:
            post: Post dictionary with title, content, and metadata
            
        Returns:
            Grading result dictionary or None if grading fails
        """
        try:
            # Prepare the prompt
            prompt = self.grading_prompt_template.format(
                title=post['title'],
                content=post.get('cleaned_content', post['content']),
                word_count=post.get('word_count', 0)
            )
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert content curator for viral TikTok videos. Respond only with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            # Parse the response
            response_text = response.choices[0].message.content.strip()
            
            # Extract JSON from response (in case there's extra text)
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                response_text = json_match.group()
            
            grading_result = json.loads(response_text)

            # Extract scores with defaults if missing, handle potential type errors
            try:
                storytelling_score = float(grading_result.get('storytelling_score', 0))
            except (ValueError, TypeError):
                storytelling_score = 0.0

            try:
                engagement_potential_score = float(grading_result.get('engagement_potential_score', 0))
            except (ValueError, TypeError):
                engagement_potential_score = 0.0

            try:
                viral_likelihood_score = float(grading_result.get('viral_likelihood_score', 0))
            except (ValueError, TypeError):
                viral_likelihood_score = 0.0

            try:
                content_uniqueness_score = float(grading_result.get('content_uniqueness_score', 0))
            except (ValueError, TypeError):
                content_uniqueness_score = 0.0

            try:
                execution_flexibility_score = float(grading_result.get('execution_flexibility_score', 0))
            except (ValueError, TypeError):
                execution_flexibility_score = 0.0

            length_penalty = post.get('length_penalty', 1.0)

            # Calculate composite score if not provided (scores are now 0-20, so normalize to 0-10 scale)
            if 'composite_score' not in grading_result or grading_result['composite_score'] == 0:
                # Weighted composite score - normalize 0-20 scores to 0-10 scale
                normalized_storytelling = storytelling_score / 2.0
                normalized_engagement = engagement_potential_score / 2.0
                normalized_viral = viral_likelihood_score / 2.0
                normalized_uniqueness = content_uniqueness_score / 2.0
                normalized_flexibility = execution_flexibility_score / 2.0

                composite_score = (
                    normalized_storytelling * 0.25 +
                    normalized_engagement * 0.25 +
                    normalized_viral * 0.25 +
                    normalized_uniqueness * 0.15 +
                    normalized_flexibility * 0.10
                ) * length_penalty
                grading_result['composite_score'] = composite_score
            else:
                # Use provided composite score but apply length penalty
                grading_result['composite_score'] = float(grading_result['composite_score']) * length_penalty

            grading_result['length_penalty'] = length_penalty

            # Ensure all score fields exist with defaults
            grading_result['storytelling_score'] = storytelling_score
            grading_result['engagement_potential_score'] = engagement_potential_score
            grading_result['viral_likelihood_score'] = viral_likelihood_score
            grading_result['content_uniqueness_score'] = content_uniqueness_score
            grading_result['execution_flexibility_score'] = execution_flexibility_score
            
            # Generate filename summary if not provided or invalid
            if not grading_result.get('summary') or len(grading_result['summary'].split()) > 5:
                grading_result['summary'] = self._create_filename_summary(
                    post['title'],
                    post.get('cleaned_content', post['content'])
                )

            # Ensure summary is filename-safe
            grading_result['summary'] = re.sub(r'[^a-z0-9_]', '', grading_result['summary'].lower())

            # Ensure word_count field exists
            if 'word_count' not in grading_result:
                grading_result['word_count'] = post.get('word_count', 0)

            # Ensure reasoning field exists
            if 'reasoning' not in grading_result:
                grading_result['reasoning'] = "Automated scoring with default values"
            
            logging.debug(f"Graded post {post['id']}: composite_score={composite_score:.2f}")
            return grading_result
            
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response for post {post['id']}: {e}")
            logging.error(f"Response was: {response_text}")
            return None
        except Exception as e:
            logging.error(f"Error grading post {post['id']}: {e}")
            return None
    
    def grade_posts_batch(self, posts: List[Dict]) -> List[Dict]:
        """
        Grade multiple posts and return them sorted by composite score.
        
        Args:
            posts: List of post dictionaries
            
        Returns:
            List of posts with grading results, sorted by composite score (descending)
        """
        graded_posts = []
        
        for i, post in enumerate(posts):
            logging.info(f"Grading post {i+1}/{len(posts)}: {post['title'][:50]}...")
            
            grading_result = self.grade_post(post)
            if grading_result:
                # Add grading results to post
                post.update(grading_result)
                graded_posts.append(post)
            else:
                logging.warning(f"Failed to grade post {post['id']}")
        
        # Sort by composite score (descending)
        graded_posts.sort(key=lambda x: x.get('composite_score', 0), reverse=True)
        
        logging.info(f"Successfully graded {len(graded_posts)}/{len(posts)} posts")
        return graded_posts
    
    def select_top_posts(self, graded_posts: List[Dict], num_videos: int) -> List[Dict]:
        """
        Select the top N posts for video generation.
        
        Args:
            graded_posts: List of graded posts sorted by score
            num_videos: Number of videos to generate (max 10)
            
        Returns:
            List of top posts for video generation
        """
        num_videos = min(num_videos, 10)  # Enforce max limit
        num_videos = min(num_videos, len(graded_posts))  # Don't exceed available posts
        
        top_posts = graded_posts[:num_videos]
        
        logging.info(f"Selected top {len(top_posts)} posts for video generation:")
        for i, post in enumerate(top_posts):
            logging.info(f"  {i+1}. {post['title'][:50]}... (score: {post['composite_score']:.2f})")
        
        return top_posts

def test_post_grader():
    """Test the post grader with sample posts."""
    try:
        grader = PostGrader()
        
        # Sample posts for testing
        test_posts = [
            {
                'id': 'test1',
                'title': 'AITA for not sharing my cookies with my mother-in-law?',
                'content': 'My mother-in-law came over and demanded I give her all my homemade cookies. I said no because I made them for my kids. She got really upset and left. Now my husband is mad at me.',
                'word_count': 35,
                'length_penalty': 0.8
            },
            {
                'id': 'test2',
                'title': 'AITA for telling my neighbor their music is too loud?',
                'content': 'My neighbor plays loud music every night until 2 AM. I asked them politely to turn it down but they ignored me. So I called the landlord and now they hate me.',
                'word_count': 32,
                'length_penalty': 0.8
            }
        ]
        
        # Grade the posts
        graded_posts = grader.grade_posts_batch(test_posts)
        
        print(f"\nGraded {len(graded_posts)} posts:")
        for post in graded_posts:
            print(f"\nTitle: {post['title']}")
            print(f"Summary: {post['summary']}")
            print(f"Storytelling Score: {post.get('storytelling_score', 0)}")
            print(f"Engagement Potential Score: {post.get('engagement_potential_score', 0)}")
            print(f"Viral Likelihood Score: {post.get('viral_likelihood_score', 0)}")
            print(f"Content Uniqueness Score: {post.get('content_uniqueness_score', 0)}")
            print(f"Execution Flexibility Score: {post.get('execution_flexibility_score', 0)}")
            print(f"Composite Score: {post['composite_score']:.2f}")
            print(f"Reasoning: {post.get('reasoning', 'N/A')}")
            
    except Exception as e:
        print(f"Error testing post grader: {e}")
        print("Make sure you have set up your .env file with OpenAI API key")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    test_post_grader()
