#!/usr/bin/env python3
"""
<PERSON>ript to help set up B-roll videos for Reddit2Shorts.
This script creates sample videos using ffmpeg for testing purposes.
"""

import os
import subprocess
from pathlib import Path

def create_sample_video(filename, duration=90, width=1920, height=1080):
    """
    Create a sample video using ffmpeg for testing.
    
    Args:
        filename: Output filename
        duration: Video duration in seconds
        width: Video width
        height: Video height
    """
    output_path = Path('broll') / filename
    
    # Create a colorful test pattern video
    cmd = [
        'ffmpeg',
        '-f', 'lavfi',
        '-i', f'testsrc2=duration={duration}:size={width}x{height}:rate=30',
        '-f', 'lavfi',
        '-i', f'sine=frequency=1000:duration={duration}',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-pix_fmt', 'yuv420p',
        '-y',  # Overwrite output file
        str(output_path)
    ]
    
    try:
        print(f"Creating sample video: {filename}")
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"✅ Created: {output_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create {filename}: {e}")
        return False
    except FileNotFoundError:
        print("❌ ffmpeg not found. Please install ffmpeg first.")
        return False

def main():
    """Create sample B-roll videos for testing."""
    print("🎬 Setting up sample B-roll videos for Reddit2Shorts")
    print("=" * 50)
    
    # Ensure broll directory exists
    broll_dir = Path('broll')
    broll_dir.mkdir(exist_ok=True)
    
    # Check if ffmpeg is available
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ ffmpeg not found. Please install ffmpeg first:")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu: sudo apt install ffmpeg")
        print("  Windows: Download from https://ffmpeg.org/")
        return 1
    
    # Create sample videos
    sample_videos = [
        ('minecraft_parkour.mp4', 120),  # 2 minutes
        ('subway_surfers.mp4', 90),      # 1.5 minutes
        ('mobile_game.mp4', 100),        # 1 minute 40 seconds
    ]
    
    created = 0
    for filename, duration in sample_videos:
        if create_sample_video(filename, duration):
            created += 1
    
    print(f"\n📊 Created {created}/{len(sample_videos)} sample videos")
    
    if created > 0:
        print("✅ Sample B-roll videos are ready!")
        print("\nNote: These are test pattern videos for development.")
        print("For production use, replace with actual gameplay footage:")
        print("  - Minecraft parkour videos")
        print("  - Subway Surfers gameplay")
        print("  - Mobile game footage")
        print("  - Any engaging, fast-paced content")
        
        print(f"\nVideos saved to: {broll_dir.absolute()}")
    else:
        print("❌ Failed to create sample videos")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
